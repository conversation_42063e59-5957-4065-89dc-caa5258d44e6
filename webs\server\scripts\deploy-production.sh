#!/bin/bash
# 生产环境部署脚本

echo "🚀 开始部署楠楠家厨到生产环境..."

# 检查是否在正确的目录
if [ ! -f "package.json" ]; then
    echo "❌ 错误: 请在 webs/server 目录下运行此脚本"
    exit 1
fi

# 1. 备份当前的 schema.prisma
echo "📦 备份当前配置..."
if [ -f "prisma/schema.prisma" ]; then
    cp prisma/schema.prisma prisma/schema.sqlite.backup
    echo "✅ 已备份 SQLite schema"
fi

# 2. 切换到 MySQL schema
echo "🔄 切换到 MySQL 数据库配置..."
cp prisma/schema.mysql.prisma prisma/schema.prisma
echo "✅ 已切换到 MySQL schema"

# 3. 切换到生产环境配置
echo "⚙️ 配置生产环境..."
if [ -f ".env.production" ]; then
    cp .env.production .env
    echo "✅ 已切换到生产环境配置"
else
    echo "❌ 错误: .env.production 文件不存在"
    echo "请先创建 .env.production 文件"
    exit 1
fi

# 4. 安装生产依赖
echo "📦 安装生产依赖..."
npm install --production

# 5. 生成 Prisma 客户端
echo "🔧 生成 Prisma 客户端..."
npm run generate

# 6. 推送数据库结构 (首次部署)
echo "🗄️ 同步数据库结构..."
read -p "是否是首次部署? (y/n): " first_deploy
if [ "$first_deploy" = "y" ]; then
    npm run db:push
    echo "✅ 数据库结构已同步"
else
    echo "⏭️ 跳过数据库结构同步"
fi

# 7. 检查 PM2 是否安装
if ! command -v pm2 &> /dev/null; then
    echo "📦 安装 PM2..."
    npm install -g pm2
fi

# 8. 创建日志目录
mkdir -p logs

# 9. 启动或重启服务
echo "🚀 启动服务..."
if pm2 list | grep -q "nannan-api"; then
    echo "🔄 重启现有服务..."
    pm2 restart nannan-api
else
    echo "🆕 启动新服务..."
    pm2 start src/app.js --name nannan-api
    pm2 save
fi

# 10. 显示服务状态
echo "📊 服务状态:"
pm2 status

echo ""
echo "✅ 部署完成!"
echo "🌐 API地址: https://www.huanglun.asia/api"
echo "📊 查看日志: pm2 logs nannan-api"
echo "🔄 重启服务: pm2 restart nannan-api"
