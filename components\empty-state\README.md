# EmptyState 缺省页组件

通用的缺省页组件，用于在数据为空时显示友好的提示界面。

## 功能特性

- 🎨 多种主题样式（light, card, minimal）
- 📏 多种尺寸规格（small, normal, large）
- 🎭 支持自定义图标和文字
- 🔘 可配置操作按钮
- ✨ 支持动画效果
- 🧩 支持插槽自定义

## 基础用法

### 1. 在页面配置中引入组件

```json
{
  "usingComponents": {
    "empty-state": "/components/empty-state/index"
  }
}
```

### 2. 基础使用

```xml
<!-- 最简单的用法 -->
<empty-state 
  wx:if="{{list.length === 0}}"
  text="暂无数据"
  description="暂时没有相关数据"
/>
```

### 3. 带操作按钮

```xml
<empty-state 
  wx:if="{{connectedUsers.length === 0}}"
  icon="friends-o"
  text="暂无关联用户"
  description="添加关联用户后可推送订单给他们"
  action-text="去添加关联用户"
  bind:action="goToUserConnection"
/>
```

### 4. 自定义主题和尺寸

```xml
<empty-state 
  theme="card"
  size="large"
  animated="{{true}}"
  text="暂无订单"
  description="您还没有任何订单记录"
  action-text="去下单"
  bind:action="goToOrder"
/>
```

## 属性说明

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| icon | String | 'friends-o' | 图标名称（van-icon） |
| iconSize | String | '60rpx' | 图标大小 |
| iconColor | String | '#D1D5DB' | 图标颜色 |
| text | String | '暂无数据' | 主要文字 |
| description | String | '' | 描述文字 |
| actionText | String | '' | 操作按钮文字 |
| actionSize | String | 'small' | 按钮大小 |
| actionType | String | 'primary' | 按钮类型 |
| actionPlain | Boolean | true | 是否为朴素按钮 |
| theme | String | '' | 主题样式 |
| size | String | 'normal' | 尺寸规格 |
| animated | Boolean | false | 是否启用动画 |
| customClass | String | '' | 自定义样式类 |

## 主题样式

### light 主题
```xml
<empty-state theme="light" />
```
- 浅灰色背景
- 虚线边框
- 适合嵌入在卡片中

### card 主题
```xml
<empty-state theme="card" />
```
- 白色背景
- 阴影效果
- 适合作为独立卡片

### minimal 主题
```xml
<empty-state theme="minimal" />
```
- 无背景
- 紧凑布局
- 适合空间有限的场景

## 尺寸规格

### small 小尺寸
```xml
<empty-state size="small" />
```

### normal 正常尺寸（默认）
```xml
<empty-state size="normal" />
```

### large 大尺寸
```xml
<empty-state size="large" />
```

## 事件

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| action | 操作按钮点击 | {timestamp: number} |

## 插槽

### icon 插槽
自定义图标内容：

```xml
<empty-state text="自定义图标">
  <view slot="icon" class="custom-icon">
    <image src="/assets/custom-empty.png" />
  </view>
</empty-state>
```

### action 插槽
自定义操作区域：

```xml
<empty-state text="自定义操作">
  <view slot="action" class="custom-actions">
    <van-button size="small" bind:click="action1">操作1</van-button>
    <van-button size="small" bind:click="action2">操作2</van-button>
  </view>
</empty-state>
```

## 使用场景

### 1. 关联用户列表为空
```xml
<empty-state 
  wx:if="{{connectedUsers.length === 0}}"
  icon="friends-o"
  text="暂无关联用户"
  description="添加关联用户后可推送订单给他们"
  action-text="去添加关联用户"
  bind:action="goToUserConnection"
/>
```

### 2. 订单列表为空
```xml
<empty-state 
  wx:if="{{orders.length === 0}}"
  icon="orders-o"
  text="暂无订单"
  description="您还没有任何订单记录"
  action-text="去下单"
  bind:action="goToOrder"
/>
```

### 3. 搜索结果为空
```xml
<empty-state 
  wx:if="{{searchResults.length === 0 && searchKeyword}}"
  icon="search"
  text="未找到相关结果"
  description="尝试使用其他关键词搜索"
  theme="minimal"
/>
```

### 4. 网络错误
```xml
<empty-state 
  wx:if="{{networkError}}"
  icon="warning-o"
  text="网络连接失败"
  description="请检查网络连接后重试"
  action-text="重新加载"
  bind:action="reload"
/>
```

## 最佳实践

1. **合适的图标**：选择与内容相关的图标
2. **友好的文案**：使用积极正面的提示语
3. **明确的操作**：提供明确的下一步操作指引
4. **一致的风格**：在同一应用中保持风格一致
5. **适当的动画**：在合适的场景使用动画增强体验

## 样式定制

可以通过 `customClass` 属性添加自定义样式：

```xml
<empty-state custom-class="my-empty-state" />
```

```scss
.my-empty-state {
  .empty-text {
    color: #ff6b6b;
  }
  
  .empty-desc {
    font-style: italic;
  }
}
