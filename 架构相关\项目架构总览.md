# 🏗️ 楠楠家厨 - 项目架构总览

## 📋 项目概述

**项目名称**: 楠楠家厨 (wx-nan)
**项目类型**: 微信小程序 + 后端管理系统
**主要功能**: 家庭菜单管理和订餐系统
**技术架构**: 微信小程序 + Express.js + PostgreSQL

> 📝 **注意**: 详细的核心架构信息请查看 `核心架构总结.md` 文件

## 🏛️ 整体架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   微信小程序      │    │   管理后台       │    │   后端API服务    │
│   (前端用户)      │    │   (管理员)       │    │   (Express.js)   │
│                 │    │                 │    │                 │
│ • 用户登录注册   │    │ • 用户管理       │    │ • RESTful API   │
│ • 菜单浏览点餐   │◄──►│ • 菜单管理       │◄──►│ • JWT认证       │
│ • 消息通知       │    │ • 订单管理       │    │ • 文件上传      │
│ • 用户关联       │    │ • 统计分析       │    │ • 推送服务      │
│ • 分组管理       │    │ • 权限控制       │    │ • 数据验证      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                       │
                                                       ▼
                                              ┌─────────────────┐
                                              │   数据库层       │
                                              │  (PostgreSQL)   │
                                              │                 │
                                              │ • Neon云数据库  │
                                              │ • Prisma ORM   │
                                              │ • 数据同步      │
                                              └─────────────────┘
```

## 📱 前端架构

### 1. 微信小程序 (根目录)

#### 技术栈
- **框架**: 微信小程序原生开发
- **UI组件**: Vant Weapp
- **状态管理**: MobX
- **样式**: SCSS + Tailwind CSS
- **工具库**: dayjs, miniprogram-api-promise

#### 目录结构
```
├── pages/                  # 页面目录
│   ├── home/              # 首页
│   ├── order/             # 点菜页面
│   ├── add_menu/          # 新增菜单
│   ├── statistics/        # 统计页面
│   ├── mine/              # 个人中心
│   ├── message/           # 消息页面
│   ├── family_message/    # 家庭消息
│   ├── notification_center/ # 通知中心
│   ├── user_connection/   # 用户关联
│   ├── user_groups/       # 用户分组
│   ├── history_menu/      # 历史菜单
│   ├── detail/            # 详情页面
│   ├── today_order/       # 今日订单
│   ├── login/             # 登录页面
│   └── register/          # 注册页面
├── components/            # 组件目录
│   ├── menu-card/         # 菜单卡片组件
│   └── refresh-list/      # 下拉刷新列表组件
├── services/              # API服务
│   └── api.js            # API接口封装
├── utils/                 # 工具函数
│   ├── request.js        # 请求封装
│   └── ui.js             # UI工具
├── styles/                # 样式文件
├── assets/                # 静态资源
└── tests/                 # 测试文件
```

#### 核心功能模块
1. **用户认证**: 微信登录、手机号注册
2. **菜单管理**: 浏览菜单、创建菜单、历史记录
3. **订单系统**: 点餐下单、订单管理、统计分析
4. **消息系统**: 实时消息、家庭消息、通知推送
5. **用户关联**: 家庭成员关联、分组管理
6. **个人中心**: 用户信息、设置管理

### 2. 管理后台 (webs/admin)

#### 技术栈
- **框架**: Vue 3 + Vite
- **UI组件**: Element Plus
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **样式**: Tailwind CSS + SCSS
- **图表**: ECharts
- **测试**: Vitest + Vue Test Utils
- **动画**: Animate.css

#### 目录结构
```
webs/admin/src/
├── views/                 # 页面视图
│   ├── dashboard/         # 仪表盘
│   ├── user/             # 用户管理
│   ├── menu/             # 菜单管理
│   ├── order/            # 订单管理
│   ├── message/          # 消息管理
│   ├── statistics/       # 统计分析
│   └── system/           # 系统管理
├── components/           # 公共组件
│   ├── CustomTable.vue   # 自定义表格
│   ├── DynamicForm.vue   # 动态表单
│   ├── EChart.vue        # 图表组件
│   └── StatsCard.vue     # 统计卡片
├── api/                  # API接口
├── stores/               # 状态管理
├── router/               # 路由配置
├── utils/                # 工具函数
├── style/                # 样式文件
└── layout/               # 布局组件
```

#### 核心功能模块
1. **用户管理**: 用户列表、权限管理、用户统计
2. **菜单管理**: 菜品管理、分类管理、菜单配置
3. **订单管理**: 订单列表、订单统计、状态管理
4. **消息管理**: 消息列表、推送管理、通知设置
5. **统计分析**: 数据图表、趋势分析、报表导出
6. **系统管理**: 权限配置、系统设置、日志管理

## 🔧 后端架构 (webs/server)

### 技术栈
- **运行时**: Node.js
- **框架**: Express.js
- **数据库**: PostgreSQL (Neon云数据库)
- **ORM**: Prisma
- **认证**: JWT
- **文件上传**: Multer
- **图床服务**: PicX
- **部署平台**: Vercel

### 目录结构
```
webs/server/src/
├── controllers/          # 控制器层
│   ├── authController.js      # 认证控制器
│   ├── userController.js      # 用户控制器
│   ├── menuController.js      # 菜单控制器
│   ├── orderController.js     # 订单控制器
│   ├── messageController.js   # 消息控制器
│   ├── notificationController.js # 通知控制器
│   ├── connectionController.js   # 用户关联控制器
│   ├── userGroupController.js    # 用户分组控制器
│   ├── dishController.js         # 菜品控制器
│   └── subscriptionController.js # 订阅消息控制器
├── routes/               # 路由层
│   ├── auth.js          # 认证路由
│   ├── users.js         # 用户路由
│   ├── menus.js         # 菜单路由
│   ├── orders.js        # 订单路由
│   ├── messages.js      # 消息路由
│   ├── notifications.js # 通知路由
│   ├── connections.js   # 用户关联路由
│   ├── userGroups.js    # 用户分组路由
│   ├── dishes.js        # 菜品路由
│   └── subscriptions.js # 订阅消息路由
├── services/            # 服务层
│   ├── notificationService.js    # 通知服务
│   ├── wechatService.js         # 微信服务
│   ├── wechatSubscriptionService.js # 微信订阅服务
│   └── picxService.js           # 图床服务
├── middlewares/         # 中间件
│   ├── auth.js         # 认证中间件
│   └── errorHandler.js # 错误处理中间件
├── utils/              # 工具函数
│   ├── jwt.js         # JWT工具
│   ├── password.js    # 密码工具
│   ├── prisma.js      # Prisma客户端
│   └── response.js    # 响应工具
└── config/            # 配置文件
```

### API架构设计

#### RESTful API端点
```
/api/auth/*              # 认证相关
├── POST /login          # 用户登录
├── POST /register       # 用户注册
└── POST /refresh        # 刷新token

/api/users/*             # 用户管理
├── GET /users/:id       # 获取用户信息
├── PUT /users/:id       # 更新用户信息
└── GET /users/family    # 获取家庭成员

/api/menus/*             # 菜单管理
├── GET /menus/today     # 获取今日菜单
├── GET /menus/history   # 获取历史菜单
├── POST /menus          # 创建菜单
├── PUT /menus/:id       # 更新菜单
└── GET /menus/statistics # 获取统计信息

/api/dishes/*            # 菜品管理
├── GET /dishes          # 获取所有菜品
├── GET /dishes/by-category # 按分类获取菜品
├── POST /dishes         # 创建菜品
└── GET /dishes/:id/detail # 获取菜品详情

/api/orders/*            # 订单管理
├── GET /orders          # 获取所有订单
├── GET /orders/today    # 获取今日订单
├── POST /orders         # 创建订单
├── PUT /orders/:id      # 更新订单
└── DELETE /orders/:id   # 删除订单

/api/messages/*          # 消息管理
├── GET /messages        # 获取消息列表
├── POST /messages       # 创建消息
├── PUT /messages/:id    # 更新消息
└── DELETE /messages/:id # 删除消息

/api/notifications/*     # 通知管理
├── GET /notifications   # 获取通知列表
├── POST /notifications  # 创建通知
├── PUT /notifications/:id/read # 标记已读
└── PUT /notifications/read-all # 全部标记已读

/api/connections/*       # 用户关联
├── GET /connections/users # 获取可关联用户
├── POST /connections/request # 发送关联申请
├── PUT /connections/:id/respond # 处理关联申请
├── GET /connections/my  # 获取我的关联
└── DELETE /connections/:id # 取消关联

/api/user-groups/*       # 用户分组
├── GET /user-groups     # 获取分组列表
├── POST /user-groups    # 创建分组
├── PUT /user-groups/:id # 更新分组
├── DELETE /user-groups/:id # 删除分组
└── GET /user-groups/:id/members # 获取分组成员

/api/subscriptions/*     # 订阅消息
├── POST /subscriptions/order-notification # 发送订单通知
├── POST /subscriptions/menu-notification  # 发送菜单通知
└── POST /subscriptions/test # 测试订阅消息
```

## 🗄️ 数据库架构

### 数据库技术栈
- **数据库**: PostgreSQL
- **云服务**: Neon (https://console.neon.tech/)
- **ORM**: Prisma
- **迁移管理**: Prisma Migrate
- **数据管理**: Prisma Studio

### 核心数据模型

#### 1. 用户系统
```prisma
model User {
  id            String         @id @default(cuid())
  name          String         # 用户姓名
  phone         String?        @unique # 手机号
  password      String?        # 密码
  avatar        String?        # 头像
  openid        String?        @unique # 微信openid
  role          String         @default("user") # 用户角色
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt

  # 关联关系
  orders        Order[]        # 用户订单
  messages      Message[]      # 用户消息
  notifications Notification[] # 用户通知
  sentNotifications Notification[] @relation("NotificationSender")
  sentConnections UserConnection[] @relation("ConnectionSender")
  receivedConnections UserConnection[] @relation("ConnectionReceiver")
  ownedGroups UserGroup[]     # 创建的分组
}
```

#### 2. 菜品系统
```prisma
model Dish {
  id          String     @id @default(cuid())
  name        String     # 菜品名称
  description String?    # 菜品描述
  image       String?    # 菜品图片
  categoryId  String     # 分类ID
  category    Category   @relation(fields: [categoryId], references: [id])
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  menuItems   MenuItem[] # 菜单项
}

model Category {
  id        String   @id @default(cuid())
  name      String   # 分类名称
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  dishes    Dish[]   # 分类下的菜品
}
```

#### 3. 菜单系统
```prisma
model Menu {
  id        String     @id @default(cuid())
  date      DateTime   # 菜单日期
  isToday   Boolean    @default(false) # 是否为今日菜单
  remark    String?    # 备注
  createdAt DateTime   @default(now())
  updatedAt DateTime   @updatedAt
  items     MenuItem[] # 菜单项
}

model MenuItem {
  id        String   @id @default(cuid())
  menuId    String   # 菜单ID
  dishId    String   # 菜品ID
  count     Int      # 菜品数量
  menu      Menu     @relation(fields: [menuId], references: [id], onDelete: Cascade)
  dish      Dish     @relation(fields: [dishId], references: [id])
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}
```

#### 4. 订单系统
```prisma
model Order {
  id         String    @id @default(cuid())
  userId     String    # 用户ID
  user       User      @relation(fields: [userId], references: [id])
  items      Json      # 订单项JSON数据
  remark     String?   # 订单备注
  diningTime DateTime? # 用餐时间
  status     String    @default("pending") # 订单状态
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt
}
```

#### 5. 消息通知系统
```prisma
model Message {
  id        String   @id @default(cuid())
  content   String   # 消息内容
  userId    String   # 用户ID
  user      User     @relation(fields: [userId], references: [id])
  read      Boolean  @default(false) # 是否已读
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Notification {
  id        String   @id @default(cuid())
  title     String   @default("新通知") # 通知标题
  content   String   # 通知内容
  type      String   @default("general") # 通知类型
  userId    String   # 接收用户ID
  user      User     @relation(fields: [userId], references: [id])
  senderId  String?  # 发送用户ID
  sender    User?    @relation("NotificationSender", fields: [senderId], references: [id])
  data      String?  # 额外数据JSON
  read      Boolean  @default(false) # 是否已读
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}
```

#### 6. 用户关联系统
```prisma
model UserConnection {
  id          String   @id @default(cuid())
  senderId    String   # 发起关联用户ID
  receiverId  String   # 接收关联用户ID
  status      String   @default("pending") # 关联状态: pending, accepted, rejected
  message     String?  # 申请消息
  remark      String?  # 关联备注
  groupId     String?  # 所属分组ID
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  # 关联关系
  sender      User     @relation("ConnectionSender", fields: [senderId], references: [id])
  receiver    User     @relation("ConnectionReceiver", fields: [receiverId], references: [id])
  group       UserGroup? @relation(fields: [groupId], references: [id])
}

model UserGroup {
  id          String   @id @default(cuid())
  name        String   # 分组名称
  description String?  # 分组描述
  color       String?  # 分组颜色
  ownerId     String   # 分组创建者ID
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  # 关联关系
  owner       User     @relation(fields: [ownerId], references: [id])
  connections UserConnection[] # 分组内的关联
}
```

### 数据库索引优化
```sql
-- 用户相关索引
CREATE INDEX idx_user_phone ON User(phone);
CREATE INDEX idx_user_openid ON User(openid);

-- 菜品相关索引
CREATE INDEX idx_dish_category ON Dish(categoryId);

-- 菜单相关索引
CREATE INDEX idx_menu_date ON Menu(date);
CREATE INDEX idx_menu_today ON Menu(isToday);
CREATE INDEX idx_menuitem_menu ON MenuItem(menuId);
CREATE INDEX idx_menuitem_dish ON MenuItem(dishId);

-- 订单相关索引
CREATE INDEX idx_order_user ON Order(userId);
CREATE INDEX idx_order_status ON Order(status);
CREATE INDEX idx_order_created ON Order(createdAt);

-- 消息相关索引
CREATE INDEX idx_message_user ON Message(userId);
CREATE INDEX idx_message_read ON Message(read);
CREATE INDEX idx_notification_user ON Notification(userId);
CREATE INDEX idx_notification_type ON Notification(type);
CREATE INDEX idx_notification_read ON Notification(read);

-- 用户关联相关索引
CREATE INDEX idx_connection_sender ON UserConnection(senderId);
CREATE INDEX idx_connection_receiver ON UserConnection(receiverId);
CREATE INDEX idx_connection_status ON UserConnection(status);
CREATE INDEX idx_usergroup_owner ON UserGroup(ownerId);
```

## 🎨 UI/UX设计系统

### 设计规范
- **主色调**: 蓝色系 (#3B82F6)
- **辅助色**: 灰色系 (#6B7280, #9CA3AF)
- **成功色**: 绿色 (#10B981)
- **警告色**: 黄色 (#F59E0B)
- **错误色**: 红色 (#EF4444)
- **背景色**: 白色 (#FFFFFF)

### 组件设计
1. **小程序组件**
   - Vant Weapp UI组件库
   - 自定义菜单卡片组件
   - 下拉刷新列表组件
   - Tailwind CSS样式系统

2. **管理后台组件**
   - Element Plus组件库
   - 自定义表格组件
   - 动态表单组件
   - ECharts图表组件
   - 统计卡片组件

### 响应式设计
- **小程序**: 适配不同尺寸手机屏幕
- **管理后台**: 支持桌面端和平板端
- **断点设置**: 基于Tailwind CSS断点系统

## 🧪 测试架构

### 测试策略
1. **单元测试**: 核心业务逻辑测试
2. **集成测试**: API接口测试
3. **端到端测试**: 完整功能流程测试
4. **性能测试**: 接口响应时间测试

### 测试工具
- **小程序**: Jest + 自定义测试工具
- **管理后台**: Vitest + Vue Test Utils
- **后端**: 自定义API测试脚本
- **数据库**: Prisma测试工具

### 测试覆盖
```
tests/
├── components/           # 组件测试
├── pages/               # 页面测试
├── integration/         # 集成测试
├── utils/               # 工具函数测试
└── setup.js            # 测试配置
```

## 🚀 部署架构

### 部署环境
- **开发环境**: 本地开发服务器
- **测试环境**: 测试服务器
- **生产环境**: 云端部署

### 部署方案
1. **后端服务**: Vercel Serverless部署
2. **数据库**: Neon PostgreSQL云数据库
3. **图床服务**: PicX免费图床
4. **小程序**: 微信开发者工具发布
5. **管理后台**: 静态资源托管

### CI/CD流程
```
开发 → 测试 → 构建 → 部署 → 监控
 ↓      ↓      ↓      ↓      ↓
代码   单元   打包   发布   日志
提交   测试   构建   上线   监控
```

## 📦 依赖管理

### 小程序依赖
```json
{
  "dependencies": {
    "@vant/weapp": "^1.11.6",
    "dayjs": "^1.11.11",
    "miniprogram-api-promise": "^1.0.4",
    "mobx-miniprogram": "^6.12.3",
    "mobx-miniprogram-bindings": "^3.0.0"
  }
}
```

### 后端依赖
```json
{
  "dependencies": {
    "@prisma/client": "^6.8.2",
    "express": "^4.18.2",
    "jsonwebtoken": "^9.0.1",
    "bcrypt": "^5.1.1",
    "cors": "^2.8.5",
    "helmet": "^7.0.0",
    "multer": "^1.4.5-lts.1"
  }
}
```

### 管理后台依赖
```json
{
  "dependencies": {
    "vue": "^3.5.12",
    "element-plus": "^2.8.8",
    "vue-router": "^4.4.5",
    "pinia": "^2.2.6",
    "echarts": "^5.5.1",
    "axios": "^1.7.7"
  }
}
```

## 🌟 项目特色功能

### 1. 家庭菜单管理系统
- **智能菜单推荐**: 基于历史数据推荐菜品
- **菜单分享**: 支持家庭成员间菜单分享
- **菜品分类管理**: 多级分类，便于管理
- **菜单统计分析**: 菜品偏好统计，营养分析

### 2. 用户关联系统
- **家庭成员关联**: 支持家庭成员互相关联
- **分组管理**: 自定义分组，灵活管理关联关系
- **关联申请流程**: 发送申请 → 审核 → 建立关联
- **权限控制**: 不同关联关系对应不同权限

### 3. 消息通知系统
- **实时消息推送**: 基于WebSocket的实时消息
- **微信订阅消息**: 集成微信小程序订阅消息
- **消息分类**: 系统消息、家庭消息、订单消息等
- **消息状态管理**: 已读/未读状态跟踪

### 4. 智能订单系统
- **快速下单**: 一键选择菜品，快速生成订单
- **订单统计**: 个人订单统计，家庭订单汇总
- **用餐时间预约**: 支持预约用餐时间
- **订单状态跟踪**: 待处理、进行中、已完成等状态

### 5. 数据统计分析
- **菜品偏好分析**: 统计最受欢迎的菜品
- **订单趋势分析**: 订单量趋势，用餐习惯分析
- **用户活跃度**: 用户登录频率，使用习惯分析
- **营养搭配建议**: 基于菜品营养信息的搭配建议

### 6. 权限管理系统
- **多角色支持**: 管理员、普通用户、家庭成员等
- **功能权限控制**: 不同角色对应不同功能权限
- **数据权限控制**: 用户只能访问自己的数据
- **操作日志记录**: 重要操作的日志记录

## 🛠️ 开发指南

### 环境搭建

#### 1. 开发环境要求
- **Node.js**: >= 16.0.0
- **npm/pnpm/yarn**: 最新版本
- **微信开发者工具**: 最新版本
- **PostgreSQL**: >= 13.0 (或使用Neon云数据库)

#### 2. 项目启动流程

**后端服务启动**:
```bash
cd webs/server
npm install
npm run generate    # 生成Prisma客户端
npm run db:push     # 同步数据库模式
npm run dev         # 启动开发服务器
```

**管理后台启动**:
```bash
cd webs/admin
npm install
npm run dev         # 启动开发服务器
```

**小程序开发**:
1. 使用微信开发者工具打开项目根目录
2. 配置小程序AppID
3. 编译运行

#### 3. 数据库管理
```bash
# 查看数据库状态
npm run db:test

# 启动Prisma Studio
npm run studio

# 数据导出
npm run db:export

# 数据导入
npm run db:import <文件名>

# 清空数据
npm run db:clear

# 生成测试数据
npm run db:seed
```
