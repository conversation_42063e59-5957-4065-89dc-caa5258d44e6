/* 注册页面样式 */
.register-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

/* 背景装饰 */
.bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 120px;
  height: 120px;
  top: 10%;
  right: 10%;
  animation-delay: 0s;
}

.circle-2 {
  width: 80px;
  height: 80px;
  top: 60%;
  left: 5%;
  animation-delay: 2s;
}

.circle-3 {
  width: 60px;
  height: 60px;
  bottom: 20%;
  right: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* 头部区域 */
.header-section {
  padding: 60px 40px 40px;
  text-align: center;
  color: white;
}

.logo-container {
  margin-bottom: 20px;
}

.logo {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border: 3px solid rgba(255, 255, 255, 0.3);
}

.app-title {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 8px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.app-subtitle {
  font-size: 16px;
  opacity: 0.9;
  font-weight: 400;
}

/* 表单容器 */
.form-container {
  padding: 0 20px;
}

.form-card {
  background: white;
  border-radius: 20px;
  padding: 30px 24px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
}

/* 输入组 */
.input-group {
  margin-bottom: 24px;
  position: relative;
}

.input-label {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.input-group .van-field {
  background: #F9FAFB;
  border-radius: 12px;
  border: 1px solid #E5E7EB;
  transition: all 0.3s ease;
}

.input-group .van-field:focus-within {
  border-color: #3B82F6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.password-toggle {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  padding: 8px;
  cursor: pointer;
}

/* 注册按钮 */
.register-btn-container {
  margin: 32px 0 24px;
}

.register-btn-container .van-button {
  width: 100%;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
  transition: all 0.3s ease;
}

.register-btn-container .van-button:active {
  transform: translateY(1px);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.4);
}

/* 登录链接 */
.login-link {
  text-align: center;
  font-size: 14px;
  color: #6B7280;
}

.link-text {
  color: #3B82F6;
  font-weight: 500;
  margin-left: 4px;
}

/* 提示信息 */
.tip-section {
  padding: 0 20px;
  margin-top: 16px;
}

.tip-message {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  animation: slideIn 0.3s ease;
}

.tip-message.info {
  background: rgba(59, 130, 246, 0.1);
  color: #1D4ED8;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.tip-message.error {
  background: rgba(239, 68, 68, 0.1);
  color: #DC2626;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.tip-message.success {
  background: rgba(34, 197, 94, 0.1);
  color: #059669;
  border: 1px solid rgba(34, 197, 94, 0.2);
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 快捷注册 */
.quick-register-section {
  padding: 30px 20px 40px;
}

.divider-container {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.divider-line {
  flex: 1;
  height: 1px;
  background: rgba(255, 255, 255, 0.3);
}

.divider-text {
  padding: 0 16px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
}

.quick-register-buttons {
  display: flex;
  justify-content: center;
}

.wechat-register-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  background: white;
  border: none;
  border-radius: 12px;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.wechat-register-btn:active {
  transform: translateY(1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 响应式适配 */
@media (max-height: 700px) {
  .header-section {
    padding: 40px 40px 30px;
  }
  
  .app-title {
    font-size: 24px;
  }
  
  .form-card {
    padding: 24px 20px;
  }
  
  .input-group {
    margin-bottom: 20px;
  }
}
