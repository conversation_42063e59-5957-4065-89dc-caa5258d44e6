<view class="register-container">
  <!-- 背景装饰 -->
  <view class="bg-decoration">
    <view class="circle circle-1"></view>
    <view class="circle circle-2"></view>
    <view class="circle circle-3"></view>
  </view>

  <!-- 顶部Logo区域 -->
  <view class="header-section">
    <view class="logo-container">
      <image class="logo" src="/assets/image/avator.jpg" mode="aspectFill" />
    </view>
    <view class="app-title">楠楠家厨</view>
    <view class="app-subtitle">注册新账户</view>
  </view>

  <!-- 注册表单 -->
  <view class="form-container">
    <view class="form-card">
      <!-- 用户名输入 -->
      <view class="input-group">
        <view class="input-label">
          <van-icon name="user-o" size="18px" color="#6B7280" />
          <text>用户名</text>
        </view>
        <van-field
          value="{{form.username}}"
          placeholder="请输入用户名"
          border="{{false}}"
          bind:change="onUsernameChange"
          maxlength="20"
          custom-style="padding: 12px 0; font-size: 16px;"
        />
      </view>

      <!-- 手机号输入 -->
      <view class="input-group">
        <view class="input-label">
          <van-icon name="phone-o" size="18px" color="#6B7280" />
          <text>手机号</text>
        </view>
        <van-field
          value="{{form.phone}}"
          placeholder="请输入手机号"
          border="{{false}}"
          bind:change="onPhoneChange"
          type="number"
          maxlength="11"
          custom-style="padding: 12px 0; font-size: 16px;"
        />
      </view>

      <!-- 密码输入 -->
      <view class="input-group">
        <view class="input-label">
          <van-icon name="lock" size="18px" color="#6B7280" />
          <text>密码</text>
        </view>
        <van-field
          value="{{form.password}}"
          placeholder="请输入密码（6-20位）"
          border="{{false}}"
          bind:change="onPasswordChange"
          password="{{!showPassword}}"
          maxlength="20"
          custom-style="padding: 12px 0; font-size: 16px;"
        />
        <view class="password-toggle" bindtap="togglePassword">
          <van-icon name="{{showPassword ? 'eye-o' : 'closed-eye'}}" size="18px" color="#6B7280" />
        </view>
      </view>

      <!-- 确认密码输入 -->
      <view class="input-group">
        <view class="input-label">
          <van-icon name="lock" size="18px" color="#6B7280" />
          <text>确认密码</text>
        </view>
        <van-field
          value="{{form.confirmPassword}}"
          placeholder="请再次输入密码"
          border="{{false}}"
          bind:change="onConfirmPasswordChange"
          password="{{!showConfirmPassword}}"
          maxlength="20"
          custom-style="padding: 12px 0; font-size: 16px;"
        />
        <view class="password-toggle" bindtap="toggleConfirmPassword">
          <van-icon name="{{showConfirmPassword ? 'eye-o' : 'closed-eye'}}" size="18px" color="#6B7280" />
        </view>
      </view>

      <!-- 验证码输入 -->
      <view class="input-group verification-group">
        <view class="input-label">
          <van-icon name="shield-o" size="18px" color="#6B7280" />
          <text>验证码</text>
        </view>
        <view class="verification-container">
          <van-field
            value="{{form.verificationCode}}"
            placeholder="请输入验证码"
            border="{{false}}"
            bind:change="onVerificationCodeChange"
            maxlength="6"
            custom-style="padding: 12px 0; font-size: 16px; flex: 1;"
          />
          <view 
            class="send-code-btn {{codeSending ? 'sending' : ''}}" 
            bindtap="sendVerificationCode"
          >
            <text wx:if="{{!codeSending && countdown === 0}}">发送验证码</text>
            <text wx:elif="{{codeSending}}">发送中...</text>
            <text wx:else>{{countdown}}s后重发</text>
          </view>
        </view>
      </view>

      <!-- 协议同意 -->
      <view class="agreement-section">
        <view class="agreement-checkbox" bindtap="toggleAgreement">
          <van-icon 
            name="{{agreedToTerms ? 'checked' : 'circle'}}" 
            size="18px" 
            color="{{agreedToTerms ? '#3B82F6' : '#D1D5DB'}}" 
          />
          <text class="agreement-text">
            我已阅读并同意
            <text class="link-text" bindtap="viewTerms">《用户协议》</text>
            和
            <text class="link-text" bindtap="viewPrivacy">《隐私政策》</text>
          </text>
        </view>
      </view>

      <!-- 注册按钮 -->
      <view class="register-btn-container">
        <van-button
          type="primary"
          size="large"
          loading="{{registering}}"
          disabled="{{!canRegister}}"
          bind:click="handleRegister"
          custom-style="background: linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%); border: none; border-radius: 12px; height: 48px; font-size: 16px; font-weight: 600;"
        >
          {{registering ? '注册中...' : '立即注册'}}
        </van-button>
      </view>

      <!-- 登录链接 -->
      <view class="login-link">
        <text>已有账户？</text>
        <text class="link-text" bindtap="goToLogin">立即登录</text>
      </view>
    </view>
  </view>

  <!-- 提示信息 -->
  <view class="tip-section" wx:if="{{tipMessage}}">
    <view class="tip-message {{tipType}}">
      <van-icon name="{{tipType === 'error' ? 'warning-o' : 'info-o'}}" size="16px" />
      <text>{{tipMessage}}</text>
    </view>
  </view>

  <!-- 快捷注册方式 -->
  <view class="quick-register-section">
    <view class="divider-container">
      <view class="divider-line"></view>
      <text class="divider-text">或使用以下方式注册</text>
      <view class="divider-line"></view>
    </view>
    
    <view class="quick-register-buttons">
      <button 
        class="wechat-register-btn"
        open-type="getUserInfo"
        bindgetuserinfo="handleWechatRegister"
      >
        <van-icon name="wechat" size="20px" color="#07C160" />
        <text>微信快速注册</text>
      </button>
    </view>
  </view>
</view>

<!-- Toast组件 -->
<van-toast id="van-toast" />

<!-- Dialog组件 -->
<van-dialog id="van-dialog" />
