/* 分类选择器弹窗样式 */
.category-selector-popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;

  &.show {
    opacity: 1;
    visibility: visible;

    .popup-content {
      transform: scale(1);
      opacity: 1;
    }
  }
}

.popup-content {
  background: white;
  border-radius: 24rpx;
  width: 90%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
  transform: scale(0.9);
  opacity: 0;
  transition: all 0.3s ease;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.2);
}

/* 弹窗头部 */
.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 32rpx 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.popup-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.close-icon {
  padding: 8rpx;
  color: #999;
  transition: color 0.3s ease;

  &:active {
    color: #666;
    transform: scale(0.9);
  }
}

/* 分类网格 */
.category-grid {
  padding: 32rpx 24rpx;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24rpx;
  max-height: 500rpx;
  overflow-y: auto;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  padding: 32rpx 16rpx;
  border-radius: 20rpx;
  border: 2rpx solid #f0f0f0;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  background: white;

  &:active {
    transform: scale(0.95);
  }

  &.selected {
    border-color: #667eea;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transform: translateY(-4rpx);
    box-shadow: 0 12rpx 32rpx rgba(102, 126, 234, 0.3);

    .category-label {
      color: white;
    }
  }
}

.category-icon {
  font-size: 48rpx;
  line-height: 1;
}

.category-label {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  transition: color 0.3s ease;
}

.selected-indicator {
  position: absolute;
  top: 12rpx;
  right: 12rpx;
  width: 32rpx;
  height: 32rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(8rpx);
}

/* 弹窗底部 */
.popup-footer {
  padding: 24rpx 32rpx 32rpx;
  border-top: 1rpx solid #f0f0f0;
}

.confirm-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);

  &:active {
    transform: scale(0.98);
    box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.4);
  }

  &::after {
    border: none;
  }
}
