{"name": "wx-nan-server", "version": "1.0.0", "description": "Backend server for wx-nan WeChat Mini Program", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "generate": "prisma generate", "migrate": "prisma migrate dev", "studio": "prisma studio", "db:push": "prisma db push", "deploy:prod": "bash scripts/deploy-production.sh", "switch:dev": "bash scripts/switch-to-dev.sh", "db:backup": "bash scripts/backup-db.sh"}, "keywords": ["express", "api", "wechat", "mini-program"], "author": "", "license": "ISC", "dependencies": {"@prisma/client": "^6.8.2", "axios": "^1.4.0", "bcrypt": "^5.1.1", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.0.0", "jsonwebtoken": "^9.0.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.6.5", "node-fetch": "^3.3.2"}, "devDependencies": {"node-cron": "^4.2.1", "nodemon": "^3.0.1", "prisma": "^5.19.1"}, "pnpm": {"ignoredBuiltDependencies": ["@prisma/client", "@prisma/engines", "bcrypt", "prisma"]}}