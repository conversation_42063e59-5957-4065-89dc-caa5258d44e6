const express = require('express');
const router = express.Router();
const userController = require('../controllers/userController');
const {auth, adminAuth} = require('../middlewares/auth');

// 获取用户统计信息 (需要管理员权限)
router.get('/statistics', auth, adminAuth, userController.getUserStatistics);

// 获取用户列表 (需要管理员权限)
router.get('/', auth, adminAuth, userController.getUsers);

// 获取家庭成员列表
router.get('/family', auth, userController.getFamilyMembers);

// 批量删除用户 (需要管理员权限)
router.delete('/batch', auth, adminAuth, userController.batchDeleteUsers);

// 获取指定用户
router.get('/:id', auth, userController.getUserById);

// 创建用户 (需要管理员权限)
router.post('/', auth, adminAuth, userController.createUser);

// 更新用户
router.put('/:id', auth, userController.updateUser);

// 重置用户密码 (需要管理员权限)
router.put('/:id/password', auth, adminAuth, userController.resetUserPassword);

// 删除用户 (需要管理员权限)
router.delete('/:id', auth, adminAuth, userController.deleteUser);

// 绑定手机号
router.post('/bind-phone', auth, userController.bindPhone);

module.exports = router;
