<view class="container">
  <!-- 顶部标题 -->
  <view class="header">
    <view class="header-content">
      <view class="title">用户关联</view>
      <view class="subtitle">管理您的关联用户</view>
    </view>
    <view class="header-actions">
      <van-button
        size="small"
        type="default"
        icon="records"
        bind:click="goToHistory"
      >
        申请历史
      </van-button>
    </view>
  </view>

  <!-- 标签页 -->
  <van-tabs active="{{ activeTab }}" bind:change="onTabChange" color="#6366F1">
    <van-tab title="我的关联" name="my">
      <view class="tab-content">
        <!-- 我的关联列表 -->
        <view wx:if="{{myConnections.length > 0}}" class="connection-list">
          <view wx:for="{{myConnections}}" wx:key="id" class="connection-item">
            <view class="user-info">
              <view class="avatar">
                <image
                  wx:if="{{item.connectedUser.avatar}}"
                  src="{{item.connectedUser.avatar}}"
                  mode="aspectFill"
                />
                <van-icon wx:else name="user-o" size="40rpx" color="#6366F1" />
              </view>
              <view class="info">
                <view class="name-section">
                  <view class="name">{{item.connectedUser.name}}</view>
                  <view class="role-badge {{item.connectedUser.role}}">
                    {{item.connectedUser.role === 'admin' ? '管理员' : '普通用户'}}
                  </view>
                </view>

                <view wx:if="{{item.remark}}" class="remark">
                  <van-icon name="bookmark-o" size="24rpx" />
                  <text>{{item.remark}}</text>
                </view>

                <view class="contact-info">
                  <view wx:if="{{item.connectedUser.phone}}" class="phone">
                    <van-icon name="phone-o" size="24rpx" />
                    <text>{{item.connectedUser.phone}}</text>
                  </view>
                </view>

                <view class="connection-time">
                  关联时间：{{item.createdAt}}
                </view>

                <!-- 关联状态组件 -->
                <connection-status
                  status="{{item.status}}"
                  is-sender="{{item.isSender}}"
                  created-at="{{item.createdAt}}"
                  size="small"
                  show-progress="{{false}}"
                  bind:statusclick="onConnectionStatusClick"
                />
              </view>
            </view>
            <view class="actions">
              <van-button
                size="small"
                type="danger"
                plain
                bind:click="removeConnection"
                data-id="{{item.id}}"
              >
                解除关联
              </van-button>
            </view>
          </view>
        </view>

        <!-- 空状态 -->
        <view wx:else class="empty-state">
          <van-icon name="friends-o" size="80rpx" color="#D1D5DB" />
          <view class="empty-text">暂无关联用户</view>
          <view class="empty-desc">点击"添加关联"来关联其他用户</view>
        </view>
      </view>
    </van-tab>

    <van-tab title="添加关联" name="add">
      <view class="tab-content">
        <!-- 搜索框 -->
        <view class="search-section">
          <van-search
            value="{{ searchKeyword }}"
            placeholder="搜索用户名或手机号"
            bind:search="onSearch"
            bind:change="onSearchChange"
            bind:clear="onSearchClear"
          />
        </view>

        <!-- 用户列表 -->
        <view wx:if="{{availableUsers.length > 0}}" class="user-list">
          <view wx:for="{{availableUsers}}" wx:key="id" class="user-item">
            <view class="user-info">
              <view class="avatar">
                <image
                  wx:if="{{item.avatar}}"
                  src="{{item.avatar}}"
                  mode="aspectFill"
                />
                <van-icon wx:else name="user-o" size="40rpx" color="#6366F1" />
              </view>
              <view class="info">
                <view class="name">{{item.name}}</view>
                <view class="phone" wx:if="{{item.phone}}">{{item.phone}}</view>
                <view
                  class="role"
                  >{{item.role === 'admin' ? '管理员' : '普通用户'}}</view
                >
              </view>
            </view>
            <view class="actions">
              <!-- 根据关联状态显示不同内容 -->
              <van-button
                wx:if="{{item.connectionStatus === 'none'}}"
                size="small"
                type="primary"
                bind:click="sendConnectionRequest"
                data-id="{{item.id}}"
                data-name="{{item.name}}"
                loading="{{sendingRequestUserId === item.id}}"
                disabled="{{sendingRequestUserId === item.id}}"
              >
                {{sendingRequestUserId === item.id ? '发送中...' : '发送申请'}}
              </van-button>

              <!-- 关联状态按钮 -->
              <van-tag
                wx:elif="{{item.connectionStatus === 'pending' && item.isSender}}"
                type="warning"
                size="medium"
                round
              >
                等待确认
              </van-tag>

              <van-tag
                wx:elif="{{item.connectionStatus === 'pending' && !item.isSender}}"
                type="primary"
                size="medium"
                round
              >
                待处理
              </van-tag>

              <van-tag
                wx:elif="{{item.connectionStatus === 'accepted'}}"
                type="success"
                size="medium"
                round
              >
                已关联
              </van-tag>

              <van-tag
                wx:elif="{{item.connectionStatus === 'rejected'}}"
                type="danger"
                size="medium"
                round
              >
                已拒绝
              </van-tag>
            </view>
          </view>
        </view>

        <!-- 空状态 -->
        <view wx:else class="empty-state">
          <van-icon name="search" size="80rpx" color="#D1D5DB" />
          <view class="empty-text">暂无用户</view>
          <view class="empty-desc">尝试搜索其他用户</view>
        </view>
      </view>
    </van-tab>

    <van-tab title="申请处理" name="requests">
      <view class="tab-content">
        <!-- 待处理申请列表 -->
        <view wx:if="{{pendingRequests.length > 0}}" class="request-list">
          <view wx:for="{{pendingRequests}}" wx:key="id" class="request-item">
            <view class="user-info">
              <view class="avatar">
                <image
                  wx:if="{{item.connectedUser.avatar}}"
                  src="{{item.connectedUser.avatar}}"
                  mode="aspectFill"
                />
                <van-icon wx:else name="user-o" size="40rpx" color="#6366F1" />
              </view>
              <view class="info">
                <view class="name">{{item.connectedUser.name}}</view>
                <view
                  class="message"
                  wx:if="{{item.message}}"
                  >{{item.message}}</view
                >

                <!-- 申请时间和状态 -->
                <view class="request-meta">
                  <view class="request-time">申请时间：{{item.createdAt}}</view>
                  <van-tag type="warning" size="small" round> 待处理 </van-tag>
                </view>
              </view>
            </view>
            <view class="actions">
              <van-button
                size="small"
                type="primary"
                bind:click="respondToRequest"
                data-id="{{item.id}}"
                data-action="accept"
                loading="{{respondingConnectionId === item.id}}"
                disabled="{{respondingConnectionId === item.id}}"
              >
                {{respondingConnectionId === item.id ? '处理中...' : '同意'}}
              </van-button>
              <van-button
                size="small"
                type="default"
                bind:click="respondToRequest"
                data-id="{{item.id}}"
                data-action="reject"
                style="margin-left: 16rpx;"
                loading="{{respondingConnectionId === item.id}}"
                disabled="{{respondingConnectionId === item.id}}"
              >
                {{respondingConnectionId === item.id ? '处理中...' : '拒绝'}}
              </van-button>
            </view>
          </view>
        </view>

        <!-- 空状态 -->
        <view wx:else class="empty-state">
          <van-icon name="clock-o" size="80rpx" color="#D1D5DB" />
          <view class="empty-text">暂无待处理申请</view>
          <view class="empty-desc">当有用户申请关联时会显示在这里</view>
        </view>
      </view>
    </van-tab>
  </van-tabs>
</view>

<!-- 发送申请弹窗 -->
<van-dialog
  use-slot
  title="发送关联申请"
  show="{{ showRequestDialog }}"
  show-cancel-button
  bind:confirm="confirmSendRequest"
  bind:cancel="cancelSendRequest"
>
  <view class="dialog-content">
    <view class="dialog-text">向 {{selectedUser.name}} 发送关联申请</view>
    <van-field
      value="{{ requestMessage }}"
      placeholder="请输入申请消息（可选）"
      type="textarea"
      maxlength="100"
      show-word-limit
      bind:change="onRequestMessageChange"
    />
  </view>
</van-dialog>

<!-- 全局Loading -->
<global-loading
  show="{{globalLoading.show}}"
  type="{{globalLoading.type}}"
  color="{{globalLoading.color}}"
  text="{{globalLoading.text}}"
  mask="{{globalLoading.mask}}"
/>
