.order-detail-container {
	height: 100vh;
	background-color: #f5f5f5;
	padding-bottom: 100px;
	overflow-y: scroll;
	/* 隐藏滚动条 */
	-ms-overflow-style: none;
	scrollbar-width: none;

	&::-webkit-scrollbar {
		display: none;
	}
}

/* 加载状态 */
.loading-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 50vh;
}

.loading-text {
	margin-top: 16px;
	color: #666;
	font-size: 14px;
}

/* 订单详情内容 */
.order-detail-content {
	padding: 16px;
}

/* 订单头部 */
.order-header {
	background: white;
	border-radius: 12px;
	padding: 20px;
	margin-bottom: 16px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.order-title {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 12px;
}

.order-id {
	font-size: 18px;
	font-weight: 600;
	color: #333;
}

.push-info {
	display: flex;
	align-items: center;
	padding: 12px;
	background: #f0f9ff;
	border-radius: 8px;
	border-left: 4px solid #1989fa;
}

.push-text {
	margin-left: 8px;
	color: #1989fa;
	font-size: 14px;
	flex: 1;
}

.contact-btn {
	background: #1989fa;
	color: white;
	border: none;
	border-radius: 16px;
	padding: 4px 12px;
	font-size: 12px;
	margin-left: 8px;
}

/* 时间信息卡片 */
.time-card {
	background: white;
	border-radius: 12px;
	padding: 20px;
	margin-bottom: 16px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.time-item {
	display: flex;
	align-items: center;
	margin-bottom: 16px;
}

.time-item:last-child {
	margin-bottom: 0;
}

.time-info {
	margin-left: 12px;
	flex: 1;
}

.time-label {
	display: block;
	color: #666;
	font-size: 12px;
	margin-bottom: 4px;
}

.time-value {
	display: block;
	color: #333;
	font-size: 14px;
	font-weight: 500;
}

/* 通用卡片样式 */
.menu-card,
.items-card,
.remark-card,
.user-card {
	background: white;
	border-radius: 12px;
	padding: 20px;
	margin-bottom: 16px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.card-header {
	display: flex;
	align-items: center;
	margin-bottom: 16px;
	padding-bottom: 12px;
	border-bottom: 1px solid #f0f0f0;
}

.card-title {
	margin-left: 8px;
	font-size: 16px;
	font-weight: 600;
	color: #333;
}

/* 菜单信息 */
.menu-info {
	display: flex;
	flex-direction: column;
}

.menu-title {
	font-size: 16px;
	font-weight: 500;
	color: #333;
	margin-bottom: 8px;
}

.menu-deleted {
	color: #ee0a24;
	font-size: 12px;
	margin-left: 8px;
}

.menu-date,
.menu-creator {
	color: #666;
	font-size: 14px;
	margin-bottom: 4px;
}

/* 订单项目 */
.order-items {
	display: flex;
	flex-direction: column;
}

.order-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 12px 0;
	border-bottom: 1px solid #f0f0f0;
}

.order-item:last-child {
	border-bottom: none;
}

.item-info {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.item-name {
	font-size: 15px;
	color: #333;
	font-weight: 500;
	margin-bottom: 4px;
}

.item-desc {
	font-size: 12px;
	color: #999;
}

.item-count {
	font-size: 14px;
	color: #666;
	font-weight: 500;
}

/* 备注 */
.remark-text {
	color: #666;
	font-size: 14px;
	line-height: 1.6;
	background: #f8f9fa;
	padding: 12px;
	border-radius: 8px;
}

/* 用户信息 */
.user-info {
	display: flex;
	align-items: center;
}

.user-avatar {
	width: 48px;
	height: 48px;
	border-radius: 24px;
	margin-right: 12px;
}

.user-details {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.user-name {
	font-size: 16px;
	font-weight: 500;
	color: #333;
	margin-bottom: 4px;
}

.user-phone {
	font-size: 14px;
	color: #666;
}