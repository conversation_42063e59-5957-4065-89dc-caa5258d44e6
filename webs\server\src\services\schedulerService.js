const cron = require('node-cron');
const {PrismaClient} = require('@prisma/client');

const prisma = new PrismaClient();

class SchedulerService {
  constructor() {
    this.tasks = new Map();
  }

  // 启动所有定时任务
  start() {
    console.log('🕐 启动定时任务服务...');
    
    // 每天凌晨2点清理旧留言
    this.scheduleMessageCleanup();
    
    console.log('✅ 定时任务服务启动完成');
  }

  // 停止所有定时任务
  stop() {
    console.log('🛑 停止定时任务服务...');
    
    this.tasks.forEach((task, name) => {
      task.stop();
      console.log(`⏹️ 停止任务: ${name}`);
    });
    
    this.tasks.clear();
    console.log('✅ 定时任务服务已停止');
  }

  // 清理旧留言的定时任务
  scheduleMessageCleanup() {
    const taskName = 'messageCleanup';
    
    // 每天凌晨2点执行 (0 2 * * *)
    const task = cron.schedule('0 2 * * *', async () => {
      try {
        console.log('🧹 开始执行留言清理任务...');
        
        // 计算5天前的时间
        const fiveDaysAgo = new Date();
        fiveDaysAgo.setDate(fiveDaysAgo.getDate() - 5);

        // 删除5天前的留言
        const result = await prisma.message.deleteMany({
          where: {
            createdAt: {
              lt: fiveDaysAgo
            }
          }
        });

        console.log(`🧹 留言清理完成，删除了 ${result.count} 条5天前的旧留言`);
        
        // 记录清理日志
        await this.logCleanupResult(result.count, fiveDaysAgo);
        
      } catch (error) {
        console.error('❌ 留言清理任务执行失败:', error);
      }
    }, {
      scheduled: false, // 不立即启动
      timezone: 'Asia/Shanghai' // 设置时区
    });

    // 启动任务
    task.start();
    this.tasks.set(taskName, task);
    
    console.log(`📅 已安排留言清理任务: 每天凌晨2点执行`);
  }

  // 记录清理结果日志
  async logCleanupResult(deletedCount, cleanupDate) {
    try {
      // 这里可以记录到数据库或日志文件
      const logEntry = {
        task: 'messageCleanup',
        deletedCount,
        cleanupDate,
        executedAt: new Date()
      };
      
      console.log('📝 清理日志:', logEntry);
      
      // 如果需要，可以将日志保存到数据库
      // await prisma.cleanupLog.create({ data: logEntry });
      
    } catch (error) {
      console.error('记录清理日志失败:', error);
    }
  }

  // 手动执行留言清理（用于测试）
  async manualMessageCleanup() {
    try {
      console.log('🧹 手动执行留言清理...');
      
      const fiveDaysAgo = new Date();
      fiveDaysAgo.setDate(fiveDaysAgo.getDate() - 5);

      const result = await prisma.message.deleteMany({
        where: {
          createdAt: {
            lt: fiveDaysAgo
          }
        }
      });

      console.log(`🧹 手动清理完成，删除了 ${result.count} 条5天前的旧留言`);
      
      await this.logCleanupResult(result.count, fiveDaysAgo);
      
      return {
        success: true,
        deletedCount: result.count,
        cleanupDate: fiveDaysAgo
      };
      
    } catch (error) {
      console.error('❌ 手动留言清理失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // 获取任务状态
  getTaskStatus() {
    const status = {};
    
    this.tasks.forEach((task, name) => {
      status[name] = {
        running: task.running || false,
        lastDate: task.lastDate || null,
        nextDate: task.nextDate || null
      };
    });
    
    return status;
  }
}

// 创建单例实例
const schedulerService = new SchedulerService();

module.exports = schedulerService;
