import {createRouter, createWebHistory} from 'vue-router';
import Layout from '@/layout/index.vue';

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/login/index.vue'),
    meta: {title: '登录'}
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/index.vue'),
        meta: {title: '仪表盘', icon: 'dashboard'}
      }
    ]
  },
  {
    path: '/menu',
    component: Layout,
    redirect: '/menu/dishes',
    meta: {title: '菜单管理', icon: 'menu'},
    children: [
      {
        path: 'dishes',
        name: 'DishList',
        component: () => import('@/views/menu/dishes.vue'),
        meta: {title: '菜品管理', icon: 'dish'}
      },
      {
        path: 'categories',
        name: 'CategoryList',
        component: () => import('@/views/menu/categories.vue'),
        meta: {title: '分类管理', icon: 'category'}
      },
      {
        path: 'today',
        name: 'TodayMenu',
        component: () => import('@/views/menu/today.vue'),
        meta: {title: '今日菜单', icon: 'today'}
      },
      {
        path: 'history',
        name: 'MenuHistory',
        component: () => import('@/views/menu/history.vue'),
        meta: {title: '历史菜单', icon: 'history'}
      }
    ]
  },
  {
    path: '/order',
    component: Layout,
    redirect: '/order/list',
    meta: {title: '订单管理', icon: 'order'},
    children: [
      {
        path: 'list',
        name: 'OrderList',
        component: () => import('@/views/order/list.vue'),
        meta: {title: '订单列表', icon: 'list'}
      },
      {
        path: 'today',
        name: 'TodayOrders',
        component: () => import('@/views/order/today.vue'),
        meta: {title: '今日订单', icon: 'today'}
      },
      {
        path: 'statistics',
        name: 'OrderStatistics',
        component: () => import('@/views/order/statistics.vue'),
        meta: {title: '订单统计', icon: 'chart'}
      }
    ]
  },
  {
    path: '/user',
    component: Layout,
    redirect: '/user/list',
    meta: {title: '用户管理', icon: 'user'},
    children: [
      {
        path: 'list',
        name: 'UserList',
        component: () => import('@/views/user/index.vue'),
        meta: {title: '用户列表', icon: 'list'}
      },
      {
        path: 'connections',
        name: 'UserConnections',
        component: () => import('@/views/user/connections.vue'),
        meta: {title: '用户关联', icon: 'link'}
      },
      {
        path: 'groups',
        name: 'UserGroups',
        component: () => import('@/views/user/groups.vue'),
        meta: {title: '用户分组', icon: 'group'}
      }
    ]
  },
  {
    path: '/message',
    component: Layout,
    redirect: '/message/family',
    meta: {title: '消息管理', icon: 'message'},
    children: [
      {
        path: 'family',
        name: 'FamilyMessages',
        component: () => import('@/views/message/family.vue'),
        meta: {title: '家庭留言', icon: 'chat'}
      },
      {
        path: 'notifications',
        name: 'NotificationList',
        component: () => import('@/views/message/notifications.vue'),
        meta: {title: '系统通知', icon: 'bell'}
      }
    ]
  },
  {
    path: '/analytics',
    component: Layout,
    redirect: '/analytics/overview',
    meta: {title: '数据分析', icon: 'chart'},
    children: [
      {
        path: 'overview',
        name: 'AnalyticsOverview',
        component: () => import('@/views/analytics/overview.vue'),
        meta: {title: '数据概览', icon: 'dashboard'}
      },
      {
        path: 'dishes',
        name: 'DishAnalytics',
        component: () => import('@/views/analytics/dishes.vue'),
        meta: {title: '菜品分析', icon: 'dish'}
      },
      {
        path: 'users',
        name: 'UserAnalytics',
        component: () => import('@/views/analytics/users.vue'),
        meta: {title: '用户分析', icon: 'user'}
      }
    ]
  },
  // 404页面 - 必须放在最后
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/404.vue'),
    meta: {title: '页面未找到'}
  }
];

const router = createRouter({
  history: createWebHistory(),
  routes
});

// 路由守卫
router.beforeEach((to, _from, next) => {
  // 设置页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title} - 楠楠家厨管理系统`;
  }

  // 检查登录状态
  const token = localStorage.getItem('admin_token');

  if (to.path === '/login') {
    next();
  } else if (!token) {
    next('/login');
  } else {
    next();
  }
});

export default router;
