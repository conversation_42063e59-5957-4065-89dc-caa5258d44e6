<view class="container">
	<view class="page-header">
		<view class="page-title">
			{{viewingToday ? '今日菜单' : '历史菜单'}}
		</view>
	</view>

	<!-- 使用刷新列表组件 -->
	<refresh-list id="refresh" height="{{refreshListHeight}}" refresher-enabled="{{true}}" show-load-more="{{true}}" load-more-status="{{loadMoreStatus}}" is-empty="{{historyMenus.length === 0}}" loading="{{loading}}" empty-text="{{viewingToday ? '今日暂无菜单' : '暂无历史菜单'}}" empty-image="/assets/image/empty_cart.svg" show-empty-action="{{true}}" empty-action-text="刷新数据" bind:refresh="onRefresh" bind:loadmore="onLoadMore" bind:emptyaction="onEmptyAction">
		<view class="history-section">
			<view class="history-list">
				<view wx:for="{{historyMenus}}" wx:key="id" class="history-card {{item.isToday ? 'today-card' : ''}}">
					<view class="history-date">
						<van-icon name="calendar-o" class="icon-margin" />{{item.date}}
						<view wx:if="{{item.isToday}}" class="today-tag">今日</view>
					</view>
					<view class="history-summary">{{item.summary}}</view>
					<view class="history-remark">
						<van-icon name="notes-o" class="icon-margin" />备注：{{item.remark || '无'}}
					</view>
					<view class="history-dishes">
						<view class="dishes-title">菜品清单：</view>
						<view class="dishes-list">
							<text wx:for="{{item.dishes}}" wx:for-item="dish" wx:key="id" class="dish-item">
								{{dish.name}} x{{dish.count}}
							</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</refresh-list>

	<view class="back-btn" bindtap="goBack">
		<van-icon name="arrow-left" class="icon-margin" />返回
	</view>
</view>