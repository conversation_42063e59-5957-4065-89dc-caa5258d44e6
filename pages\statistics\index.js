const {menuApi, orderApi, dishApi} = require('../../services/api');

Page({
  data: {
    loading: true,
    weeklyFavorite: '加载中...',
    monthlyTotal: 0,
    healthTip: '正在分析您的饮食习惯...',
    hotRank: [],
    statistics: {
      todayOrders: 0,
      totalDishes: 0,
      activeUsers: 0,
      monthlyVisits: 0
    },
    chartData: {
      categories: ['热菜', '凉菜', '汤品', '主食', '甜品'],
      data: [0, 0, 0, 0, 0]
    }
  },

  onLoad() {
    // 加载统计数据
    this.loadAllStatistics();
  },

  onShow() {
    // 页面显示时刷新数据
    this.loadAllStatistics();
  },

  // 加载所有统计数据
  async loadAllStatistics() {
    try {
      await Promise.all([
        this.getStatisticsData(),
        this.getOrderStatistics(),
        this.getHotRankData()
      ]);

      this.setData({loading: false});
    } catch (error) {
      console.error('加载统计数据失败:', error);
      this.setData({loading: false});
      wx.showToast({
        title: '加载数据失败',
        icon: 'none'
      });
    }
  },

  // 获取基础统计数据
  async getStatisticsData() {
    try {
      const result = await menuApi.getStatistics();

      if (result.code === 200) {
        const stats = result.data;

        this.setData({
          statistics: {
            todayOrders: stats.todayOrders || 0,
            totalDishes: stats.totalDishes || 0,
            activeUsers: stats.activeUsers || 0,
            monthlyVisits: stats.monthlyVisits || 0
          }
        });

        // 生成健康建议
        this.generateHealthTip(stats);
      }
    } catch (error) {
      console.error('获取统计数据失败:', error);
    }
  },

  // 获取订单统计数据
  async getOrderStatistics() {
    try {
      const result = await orderApi.getOrders();

      if (result.code === 200) {
        let orders = result.data;

        // 确保 orders 是数组
        if (!Array.isArray(orders)) {
          console.warn('订单数据不是数组格式:', orders);
          orders = orders.list || orders.data || [];
        }

        // 计算本月订单总数
        const currentMonth = new Date().getMonth();
        const monthlyOrders = orders.filter(order => {
          const orderMonth = new Date(order.createdAt).getMonth();
          return orderMonth === currentMonth;
        });

        this.setData({
          monthlyTotal: monthlyOrders.length
        });

        // 分析分类数据
        this.analyzeOrderCategories(orders);
      }
    } catch (error) {
      console.error('获取订单统计失败:', error);
      // 设置默认值
      this.setData({
        monthlyTotal: 0
      });
    }
  },

  // 获取热门菜品排行（简化版，使用真实数据）
  async getHotRankData() {
    try {
      // 1. 获取订单数据统计菜品热度
      const orderResult = await orderApi.getOrders();

      if (orderResult.code !== 200) {
        throw new Error('获取订单数据失败');
      }

      let orders = orderResult.data;
      if (!Array.isArray(orders)) {
        orders = orders.list || orders.data || [];
      }

      // 2. 统计菜品热度（现在是异步方法）
      const dishStats = await this.calculateDishHotness(orders);

      // 3. 获取菜品详细信息（包括真实图片）
      let hotRankWithImages;
      try {
        hotRankWithImages = await this.enrichDishData(dishStats);
      } catch (error) {
        console.error('获取菜品详情失败，使用基础数据:', error);
        // 使用基础数据作为降级方案
        hotRankWithImages = dishStats.map(dish => ({
          id: dish.id,
          name: dish.name,
          orderCount: dish.orderCount,
          totalCount: dish.totalCount,
          userCount: dish.userCount,
          hotScore: dish.hotScore.toFixed(1),
          img: '/assets/image/avator.jpg',
          category: '未分类',
          description: '美味佳肴'
        }));
      }

      // 4. 设置数据
      const weeklyFavorite =
        hotRankWithImages.length > 0
          ? `${hotRankWithImages[0].name}（${hotRankWithImages[0].orderCount}次点单）`
          : '暂无数据';

      this.setData({
        hotRank: hotRankWithImages,
        weeklyFavorite
      });
    } catch (error) {
      console.error('获取热门菜品失败:', error);
      this.setData({
        hotRank: [],
        weeklyFavorite: '数据加载失败'
      });
    }
  },

  // 计算菜品热度（只统计真实存在的菜品）
  async calculateDishHotness(orders) {
    const dishStats = {};
    const weekAgo = new Date();
    weekAgo.setDate(weekAgo.getDate() - 7);

    // 过滤最近7天的订单
    const recentOrders = orders.filter(order => {
      const orderDate = new Date(order.createdAt);
      return orderDate >= weekAgo;
    });

    console.log(`📊 分析最近7天的 ${recentOrders.length} 个订单`);

    // 1. 先获取所有真实存在的菜品
    let allDishes = [];
    try {
      const dishesResult = await dishApi.getDishes();
      if (dishesResult.code === 200) {
        // 后端返回的数据格式是 { list: [...], total, page, size }
        const dishesData = dishesResult.data;
        if (dishesData && dishesData.list && Array.isArray(dishesData.list)) {
          allDishes = dishesData.list;
        } else if (Array.isArray(dishesData)) {
          // 兼容直接返回数组的情况
          allDishes = dishesData;
        } else {
          console.warn('⚠️  菜品数据格式异常:', dishesData);
          allDishes = [];
        }
        console.log(`📋 系统中共有 ${allDishes.length} 个菜品`);
      }
    } catch (error) {
      console.error('获取菜品列表失败:', error);
      // 如果获取菜品列表失败，使用订单中的数据但添加警告
      console.warn('⚠️  无法验证菜品真实性，使用订单数据');
    }

    // 2. 创建菜品ID到菜品信息的映射
    const dishMap = {};
    if (Array.isArray(allDishes) && allDishes.length > 0) {
      allDishes.forEach(dish => {
        if (dish && dish.id) {
          dishMap[dish.id] = {
            id: dish.id,
            name: dish.name,
            image: dish.image,
            category: dish.category,
            description: dish.description
          };
        }
      });
    } else {
      console.warn('⚠️  allDishes 不是有效数组，跳过菜品验证');
    }

    // 3. 统计菜品数据（只统计真实存在的菜品）
    recentOrders.forEach(order => {
      const items = JSON.parse(order.items || '[]');
      items.forEach(item => {
        const dishId = item.dishId || item.id;

        // 检查菜品是否真实存在
        if (allDishes.length > 0 && !dishMap[dishId]) {
          console.warn(`⚠️  跳过不存在的菜品: ${item.name} (ID: ${dishId})`);
          return; // 跳过不存在的菜品
        }

        const dishName = dishMap[dishId]?.name || item.name;

        if (!dishStats[dishId]) {
          dishStats[dishId] = {
            id: dishId,
            name: dishName,
            orderCount: 0,
            totalCount: 0,
            userSet: new Set(),
            realDish: dishMap[dishId] || null // 保存真实菜品信息
          };
        }

        dishStats[dishId].orderCount += 1;
        dishStats[dishId].totalCount += item.count || 1;
        dishStats[dishId].userSet.add(order.userId);
      });
    });

    // 4. 转换为数组并排序
    const validDishes = Object.values(dishStats)
      .filter(dish => {
        // 如果有菜品列表，只保留真实存在的菜品
        if (allDishes.length > 0) {
          return dish.realDish !== null;
        }
        // 如果没有菜品列表，保留所有数据但添加标记
        return true;
      })
      .map(dish => ({
        ...dish,
        userCount: dish.userSet.size,
        hotScore: dish.orderCount * 0.6 + dish.userSet.size * 0.4 // 简化评分算法
      }))
      .sort((a, b) => b.hotScore - a.hotScore)
      .slice(0, 5); // 取前5名

    console.log(
      `🏆 有效菜品排行 ${validDishes.length} 个:`,
      validDishes.map(d => d.name)
    );

    return validDishes;
  },

  // 获取菜品详细信息（包括真实图片）
  async enrichDishData(dishStats) {
    const enrichedDishes = [];

    // 检查 dishApi 是否可用
    if (typeof dishApi === 'undefined') {
      console.error('❌ dishApi 未定义，使用降级方案');
      return dishStats.map(dish => ({
        id: dish.id,
        name: dish.name,
        orderCount: dish.orderCount,
        totalCount: dish.totalCount,
        userCount: dish.userCount,
        hotScore: dish.hotScore.toFixed(1),
        img: '/assets/image/avator.jpg',
        category: '未分类',
        description: '美味佳肴'
      }));
    }

    for (const dish of dishStats) {
      try {
        console.log(`🔍 处理菜品: ${dish.name} (ID: ${dish.id})`);

        // 如果已经有真实菜品信息，直接使用
        if (dish.realDish) {
          console.log(`✅ 使用已有菜品信息: ${dish.name}`);
          enrichedDishes.push({
            id: dish.id,
            name: dish.name,
            orderCount: dish.orderCount,
            totalCount: dish.totalCount,
            userCount: dish.userCount,
            hotScore: dish.hotScore.toFixed(1),
            img: dish.realDish.image || '/assets/image/avator.jpg',
            category: dish.realDish.category?.name || '未分类',
            description: dish.realDish.description || '美味佳肴'
          });
          continue;
        }

        // 如果没有真实菜品信息，尝试获取菜品详细信息
        const dishDetail = await dishApi.getDishDetail(dish.id);

        if (dishDetail && dishDetail.code === 200) {
          const dishData = dishDetail.data;
          console.log(`✅ 成功获取菜品详情: ${dish.name}`);
          enrichedDishes.push({
            id: dish.id,
            name: dish.name,
            orderCount: dish.orderCount,
            totalCount: dish.totalCount,
            userCount: dish.userCount,
            hotScore: dish.hotScore.toFixed(1),
            // 使用真实图片，如果没有则使用默认图片
            img: dishData.image || dishData.img || '/assets/image/avator.jpg', // 使用现有的头像图片作为默认图片
            category: dishData.category?.name || '未分类',
            description: dishData.description || dishData.remark || '美味佳肴'
          });
        } else {
          // 如果获取详情失败，使用基础数据
          console.warn(`⚠️  菜品 ${dish.name} API返回失败:`, dishDetail);
          enrichedDishes.push({
            id: dish.id,
            name: dish.name,
            orderCount: dish.orderCount,
            totalCount: dish.totalCount,
            userCount: dish.userCount,
            hotScore: dish.hotScore.toFixed(1),
            img: '/assets/image/avator.jpg', // 使用现有图片作为默认
            category: '未分类',
            description: '美味佳肴'
          });
        }
      } catch (error) {
        console.error(`获取菜品 ${dish.name} 详情失败:`, error);
        // 使用基础数据
        enrichedDishes.push({
          id: dish.id,
          name: dish.name,
          orderCount: dish.orderCount,
          totalCount: dish.totalCount,
          userCount: dish.userCount,
          hotScore: dish.hotScore.toFixed(1),
          img: '/assets/image/avator.jpg', // 使用现有图片作为默认
          category: '未分类',
          description: '美味佳肴'
        });
      }
    }

    return enrichedDishes;
  },

  // 分析订单分类数据
  analyzeOrderCategories(orders) {
    const categoryCount = {
      热菜: 0,
      凉菜: 0,
      汤品: 0,
      主食: 0,
      甜品: 0
    };

    // 这里可以根据实际订单数据分析分类
    // 暂时使用模拟数据
    const chartData = {
      categories: ['热菜', '凉菜', '汤品', '主食', '甜品'],
      data: [45, 20, 15, 15, 5] // 百分比数据
    };

    this.setData({chartData});
  },

  // 生成智能健康建议
  async generateHealthTip(stats) {
    try {
      console.log('🍎 开始生成智能健康建议...');

      // 获取用户最近7天的饮食数据
      const weekAgo = new Date();
      weekAgo.setDate(weekAgo.getDate() - 7);

      const result = await orderApi.getOrders();

      if (result.code === 200) {
        let orders = result.data;

        // 确保 orders 是数组
        if (!Array.isArray(orders)) {
          orders = orders.list || orders.data || [];
        }

        // 过滤最近7天的订单
        const recentOrders = orders.filter(order => {
          const orderDate = new Date(order.createdAt);
          return orderDate >= weekAgo;
        });

        if (recentOrders.length === 0) {
          this.setData({
            healthTip: '暂无饮食数据，建议保持均衡饮食！'
          });
          return;
        }

        // 分析饮食结构
        const categoryStats = {
          荤菜: 0,
          素菜: 0,
          汤品: 0,
          主食: 0,
          甜品: 0
        };

        let totalDishes = 0;
        const dishVariety = new Set();

        recentOrders.forEach(order => {
          const items = JSON.parse(order.items || '[]');
          items.forEach(item => {
            const category = item.category || '其他';
            const count = item.count || 1;

            if (categoryStats.hasOwnProperty(category)) {
              categoryStats[category] += count;
            }

            totalDishes += count;
            dishVariety.add(item.name);
          });
        });

        // 计算各类菜品比例
        const ratios = {};
        Object.keys(categoryStats).forEach(category => {
          ratios[category] =
            totalDishes > 0 ? categoryStats[category] / totalDishes : 0;
        });

        console.log('📊 饮食结构分析:', ratios);
        console.log('🍽️ 菜品多样性:', dishVariety.size);

        // 生成智能建议
        const healthTip = this.analyzeNutritionAndGenerateTip(
          ratios,
          dishVariety.size,
          recentOrders.length
        );

        this.setData({
          healthTip
        });
      } else {
        this.setData({
          healthTip: '无法获取饮食数据，建议保持均衡饮食！'
        });
      }
    } catch (error) {
      console.error('生成健康建议失败:', error);
      this.setData({
        healthTip: '建议保持均衡饮食，荤素搭配！'
      });
    }
  },

  // 分析营养结构并生成建议
  analyzeNutritionAndGenerateTip(ratios, varietyCount, orderCount) {
    const tips = [];

    // 营养均衡分析
    if (ratios.素菜 < 0.3) {
      tips.push('🥬 建议增加蔬菜摄入，素菜比例偏低，多吃绿叶蔬菜有益健康！');
    } else if (ratios.素菜 >= 0.4) {
      tips.push('🌿 素菜摄入很棒！继续保持这种健康的饮食习惯！');
    }

    if (ratios.汤品 < 0.1) {
      tips.push('🍲 建议适量饮汤，有助于消化吸收和营养补充！');
    }

    if (ratios.甜品 > 0.2) {
      tips.push('🍰 甜品摄入较多，建议适量控制，保持身材健康！');
    }

    if (ratios.荤菜 > 0.6) {
      tips.push('🥩 荤菜比例较高，建议增加素菜搭配，营养更均衡！');
    }

    // 多样性分析
    if (varietyCount < 5) {
      tips.push('🌈 菜品种类较少，建议尝试更多不同的菜品，营养更全面！');
    } else if (varietyCount >= 10) {
      tips.push('🎉 菜品搭配很丰富！营养摄入很全面，继续保持！');
    }

    // 用餐频率分析
    if (orderCount < 3) {
      tips.push('⏰ 用餐频率较低，建议规律用餐，保持健康作息！');
    }

    // 季节性建议
    const month = new Date().getMonth() + 1;
    if (month >= 6 && month <= 8) {
      // 夏季
      tips.push('☀️ 夏季建议多吃清淡菜品，少油少盐，多补充水分！');
    } else if (month >= 12 || month <= 2) {
      // 冬季
      tips.push('❄️ 冬季可适当增加温补菜品，注意营养均衡！');
    }

    // 如果没有特殊建议，给出通用建议
    if (tips.length === 0) {
      const generalTips = [
        '🌟 您的饮食搭配很均衡，继续保持这种健康的饮食习惯！',
        '💪 营养摄入良好，建议保持规律用餐，适量运动！',
        '🎯 饮食结构合理，注意食物新鲜度和烹饪方式！'
      ];
      tips.push(generalTips[Math.floor(Math.random() * generalTips.length)]);
    }

    // 随机选择一个建议，或组合多个建议
    if (tips.length === 1) {
      return tips[0];
    } else {
      // 优先显示营养相关的建议
      const nutritionTips = tips.filter(
        tip =>
          tip.includes('素菜') || tip.includes('荤菜') || tip.includes('营养')
      );
      if (nutritionTips.length > 0) {
        return nutritionTips[0];
      }
      return tips[0];
    }
  }
});
