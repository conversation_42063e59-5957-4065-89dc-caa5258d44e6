/* 我的菜品页面 - 小程序兼容设计 */
@import "../../../styles/miniprogram-design.scss";

/* 颜色变量 */
$green-600: #059669;
$orange-600: #ea580c;
$blue-600: #2563eb;
$red-600: #dc2626;

.container {
	@include page-container;
	@include page-container-safe;
	padding-bottom: 120rpx; // 为悬浮按钮留空间
	height: 100vh;
	overflow: hidden;
}

/* 页面头部 */
.header-section {
	background: white;
	padding: 24rpx 32rpx;
	margin-bottom: 16rpx;
	border-radius: 0 0 24rpx 24rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.title-row {
	@include flex;
	@include justify-between;
	@include items-center;
	@include mb-4;
}

.page-title {
	@include flex;
	@include items-center;
	@include gap-2;
}

.title-text {
	@include text-xl;
	@include font-bold;
	@include text-primary;
}

.stats-info {
	@include flex;
	@include items-center;
}

.stats-text {
	@include text-sm;
	color: $gray-500;
	font-weight: 500;
}

/* 筛选标签 */
.filter-tabs {
	@include flex;
	@include gap-2;
	background: $gray-50;
	padding: 8rpx;
	border-radius: 16rpx;
}

.filter-tab {
	@include flex-1;
	@include text-center;
	padding: 16rpx 8rpx;
	@include text-sm;
	font-weight: 500;
	color: $gray-600;
	border-radius: 12rpx;
	@include transition;

	&.active {
		background: white;
		color: $primary-solid;
		box-shadow: 0 2rpx 8rpx rgba(59, 130, 246, 0.15);
	}
}

/* 菜品列表 */
.dishes-list {
	padding: 0 32rpx;
}

.dish-card {
	background: white;
	border-radius: 24rpx;
	@include mb-4;
	overflow: hidden;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
	@include transition;

	&:active {
		transform: scale(0.98);
	}
}

/* 卡片头部布局 */
.dish-header-layout {
	@include flex;
	@include gap-3;
	padding: 24rpx;
}

/* 菜品图片区域 */
.dish-image-container {
	position: relative;
	width: 200rpx;
	height: 150rpx;
	@include flex-shrink-0;
	overflow: hidden;
	border-radius: 16rpx;
}

.dish-image {
	width: 200rpx;
	height: 150rpx;
	object-fit: cover;
}

.status-badge {
	position: absolute;
	top: 8rpx;
	left: 8rpx;
	padding: 4rpx 8rpx;
	border-radius: 12rpx;
	@include text-xs;
	font-weight: 600;
	backdrop-filter: blur(8rpx);

	&.published {
		background: rgba(16, 185, 129, 0.9);
		color: white;
	}

	&.unpublished {
		background: rgba(107, 114, 128, 0.9);
		color: white;
	}
}

.tags-overlay {
	position: absolute;
	bottom: 16rpx;
	left: 16rpx;
	@include flex;
	@include gap-1;
	@include flex-wrap;
}

.tag-mini {
	padding: 6rpx 12rpx;
	background: rgba(0, 0, 0, 0.6);
	color: white;
	border-radius: 12rpx;
	@include text-xs;
	font-weight: 500;
	backdrop-filter: blur(4rpx);
}

/* 菜品信息区域 */
.dish-info {
	@include flex-1;
	@include flex;
	@include flex-col;
	@include justify-between;
	min-height: 150rpx;
}

.dish-title-row {
	@include flex;
	@include justify-between;
	@include items-start;
	@include mb-2;
}

.dish-name {
	@include text-base;
	@include font-semibold;
	color: $gray-900;
	@include flex-1;
	margin-right: 16rpx;
	display: -webkit-box;
	-webkit-line-clamp: 1;
	-webkit-box-orient: vertical;
	overflow: hidden;
}

.dish-category {
	@include text-sm;
	color: $primary-solid;
	background: rgba(59, 130, 246, 0.1);
	padding: 6rpx 12rpx;
	border-radius: 12rpx;
	font-weight: 500;
}

.dish-description {
	@include mb-3;
}

.ingredients-preview {
	@include text-sm;
	color: $gray-600;
	line-height: 1.5;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
	overflow-wrap: break-word;
	/* 更标准写法 */
}

.dish-meta {
	@include flex;
	@include gap-4;
}

.meta-item {
	@include flex;
	@include items-center;
	@include gap-1;
}

.meta-text {
	@include text-xs;
	color: $gray-500;
}

/* 底部标签区域 */
.dish-tags {
	@include flex;
	@include gap-2;
	@include flex-wrap;
	padding: 0 24rpx 16rpx 24rpx;
}

.tag-item {
	padding: 6rpx 12rpx;
	background: $gray-100;
	color: $gray-600;
	border-radius: 12rpx;
	@include text-xs;
	font-weight: 500;
}

/* 操作按钮区域 */
.dish-actions {
	@include flex;
	border-top: 1rpx solid $gray-100;
}

.action-btn {
	@include flex-1;
	@include flex;
	@include items-center;
	@include justify-center;
	@include gap-1;
	padding: 24rpx 16rpx;
	@include text-sm;
	font-weight: 500;
	@include transition;
	border-right: 1rpx solid $gray-100;

	&:last-child {
		border-right: none;
	}

	&:active {
		background: $gray-50;
	}
}

.action-text {
	@include text-sm;
	font-weight: 500;
}

.status-btn {
	&.publish {
		color: $green-600;
	}

	&.unpublish {
		color: $orange-600;
	}
}

.edit-btn {
	color: $blue-600;
}

.delete-btn {
	color: $red-600;
}

/* 空状态 */
.empty-state {
	@include flex;
	@include flex-col;
	@include items-center;
	@include justify-center;
	padding: 120rpx 32rpx;
	@include text-center;
}

.empty-title {
	@include text-lg;
	@include font-semibold;
	color: $gray-600;
	@include mt-4;
	@include mb-2;
}

.empty-desc {
	@include text-sm;
	color: $gray-500;
	line-height: 1.5;
	@include mb-6;
}

.empty-action-btn {
	@include modern-btn;
	@include btn-primary;
	@include text-white;
	border: none;
	@include rounded-full;
	padding: 20rpx 32rpx;
	@include text-sm;
	@include font-medium;
	@include flex;
	@include items-center;
	@include gap-2;
}

/* 悬浮添加按钮 */
.fab-container {
	position: fixed;
	bottom: 120rpx;
	right: 32rpx;
	z-index: 100;
}

.fab-btn {
	width: 112rpx;
	height: 112rpx;
	background: linear-gradient(135deg, $primary-solid, $primary-end);
	border-radius: 50%;
	@include flex;
	@include items-center;
	@include justify-center;
	box-shadow: 0 8rpx 24rpx rgba(59, 130, 246, 0.4);
	@include transition;

	&:active {
		transform: scale(0.9);
	}
}

/* 工具类 */
.ml-2 {
	margin-left: 16rpx;
}

/* 眼睛图标样式 */
.eye-icon-container {
	@include flex;
	@include items-center;
	@include justify-center;
	width: 32rpx;
	height: 32rpx;
}

.closed-eye-icon {
	@include flex;
	@include items-center;
	@include justify-center;
	width: 32rpx;
	height: 32rpx;

	.eye-line {
		width: 24rpx;
		height: 3rpx;
		background-color: #999;
		border-radius: 2rpx;
		position: relative;

		// 添加一个小的弧度，更像闭眼
		&::before {
			content: "";
			position: absolute;
			top: -2rpx;
			left: 50%;
			transform: translateX(-50%);
			width: 20rpx;
			height: 6rpx;
			border: 2rpx solid #999;
			border-bottom: none;
			border-radius: 10rpx 10rpx 0 0;
			background: transparent;
		}
	}
}

/* Dialog样式修复 */
.van-dialog__footer {
	display: flex !important;
	justify-content: center !important;
	padding: 20rpx 0 !important;
}