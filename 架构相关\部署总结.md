# 🚀 楠楠家厨 - 部署进度跟踪

## 📋 服务器信息
- **云服务器**: 阿里云ECS 2核2GB CentOS 7.9
- **公网IP**: *************
- **域名**: www.huanglun.asia (已解析)
- **宝塔面板**: 免费版 9.6.0

## 🎯 部署进度

### ✅ 已完成
- [x] 服务器购买和配置
- [x] 域名解析设置
- [x] 宝塔面板安装
- [x] 部署脚本准备
- [x] 环境配置文件准备

### ✅ 已完成
- [x] **阶段1**: 服务器环境准备
  - [x] 1.1 创建网站 ✅ **普通网站已创建**
  - [x] 1.2 安装MySQL数据库 ✅ **MySQL 5.7.44 已安装**
  - [x] 1.3 安装Node.js环境 ✅ **Node.js 16.9.0 已安装**
  - [x] 1.4 SSL证书 ⏭️ **跳过(域名未备案)**

### ✅ 已完成
- [x] **阶段2**: 数据库配置
  - [x] 2.1 创建数据库 ✅ **nannan_db已创建**
  - [x] 2.2 更新生产环境配置 ✅ **密码已配置**

### ✅ 已完成
- [x] **阶段3**: 后端部署
  - [x] 3.1 上传代码 ✅ **已上传到/api**
  - [x] 3.2 部署服务 ✅ **PM2已启动**

### 🔄 进行中
- [ ] **阶段4**: 前端部署 ⏳ **当前阶段**
  - [ ] 4.1 本地打包 ⏳ **当前步骤**
  - [ ] 4.2 上传前端文件

### ⏳ 待完成
- [ ] **阶段5**: Nginx配置
- [ ] **阶段6**: 测试验证

##  关键命令速查

### 生产环境部署
```bash
# 部署到生产环境
npm run deploy:prod

# 查看服务状态
pm2 status

# 查看日志
pm2 logs nannan-api

# 重启服务
pm2 restart nannan-api
```

### 数据库配置
```bash
# 生产环境连接字符串
DATABASE_URL="mysql://nannan_user:你的密码@localhost:3306/nannan_db"
```

## 🌐 最终访问地址
- **管理后台**: https://www.huanglun.asia/admin
- **API接口**: https://www.huanglun.asia/api
- **健康检查**: https://www.huanglun.asia/api/health

## � 故障排查
```bash
# 查看PM2状态
pm2 status

# 查看错误日志
pm2 logs nannan-api --err

# 重启服务
pm2 restart nannan-api

# 检查MySQL状态
systemctl status mysql

# 检查数据库连接
mysql -u nannan_user -p5201314hl -h localhost nannan_db
```

## 🚀 自动化部署配置

### Git仓库地址
```
https://github.com/**********/wx-nan.git
```

### 自动部署流程
1. **本地开发** → 修改代码
2. **Git提交** → `git add . && git commit -m "更新功能"`
3. **推送代码** → `git push origin master`
4. **自动部署** → 访问部署页面点击部署按钮

### 部署页面访问
```
http://www.huanglun.asia/api/webs/server/deploy-trigger.php
```

### 修复常见部署问题
```bash
# 修复Prisma版本问题
npm install prisma@6.8.2 @prisma/client@6.8.2
npx prisma generate
npx prisma db push

# 检查环境变量
cat .env

# 手动执行部署脚本
cd /www/wwwroot/www.huanglun.asia/api/webs/server
bash deploy.sh
```
