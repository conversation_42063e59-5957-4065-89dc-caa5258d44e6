const express = require('express');
const router = express.Router();
const orderController = require('../controllers/orderController');
const {auth, adminAuth} = require('../middlewares/auth');
const {
  autoTimestamp,
  enhancePrismaQuery,
  validateTimestamps,
  logTimestamps
} = require('../middlewares/timestampMiddleware');

// 获取订单列表 (需要认证)
router.get('/', auth, orderController.getOrders);

// 获取订单统计信息 (需要管理员权限)
router.get('/statistics', auth, adminAuth, orderController.getOrderStatistics);

// 获取今日订单 (公开访问，用于展示)
router.get('/today', orderController.getTodayOrders);

// 获取指定订单 (需要认证)
router.get('/:id', auth, orderController.getOrderById);

// 创建订单 (需要认证)
router.post(
  '/',
  auth,
  validateTimestamps,
  autoTimestamp,
  enhancePrismaQuery,
  logTimestamps,
  orderController.createOrder
);

// 更新订单 (需要认证)
router.put(
  '/:id',
  auth,
  validateTimestamps,
  autoTimestamp,
  enhancePrismaQuery,
  logTimestamps,
  orderController.updateOrder
);

// 删除订单 (需要认证)
router.delete('/:id', auth, orderController.deleteOrder);

// 批量更新订单状态 (需要管理员权限)
router.post(
  '/batch-status',
  auth,
  adminAuth,
  orderController.batchUpdateStatus
);

module.exports = router;
