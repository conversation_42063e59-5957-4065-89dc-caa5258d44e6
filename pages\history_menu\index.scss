/* 现代化设计 */
@import "../../styles/miniprogram-design.scss";

.container {
	@include page-container;
	@include page-container-safe;

	/* 隐藏滚动条 */
	::-webkit-scrollbar {
		display: none;
		width: 0;
		height: 0;
	}

	/* 兼容性处理 */
	scrollbar-width: none; /* Firefox */
	-ms-overflow-style: none; /* IE 10+ */
}

.page-header {
	@include modern-card;
	@include card-primary;
	@include text-center;
	@include mb-4;
	padding: 32rpx;
	margin: 0 24rpx 24rpx 24rpx;
}

.page-title {
	@include text-2xl;
	@include font-bold;
	color: $primary-solid;
	text-align: center;
	background: linear-gradient(135deg, $primary-solid 0%, lighten($primary-solid, 15%) 100%);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
}

.history-section {
	max-width: 840rpx;
	margin: 0 auto;
	padding: 0 24rpx;
	position: relative;
}

.icon-margin {
	margin-right: 10rpx;
}

.history-list {
	@include flex;
	@include flex-col;
	gap: 22rpx;
}

.history-card {
	@include modern-card;
	@include card-elevated;
	@include mb-4;
	@include transition;
	padding: 32rpx;
	background: linear-gradient(135deg, rgba($white, 0.95) 0%, rgba($gray-50, 0.9) 100%);
	border: 2rpx solid rgba($primary-solid, 0.1);
	backdrop-filter: blur(8rpx);
	position: relative;

	&::before {
		content: "";
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(135deg, rgba($primary-solid, 0.03) 0%, transparent 50%);
		border-radius: $radius-xl;
		pointer-events: none;
	}

	&:hover {
		transform: translateY(-4rpx);
		@include shadow-xl;
		border-color: rgba($primary-solid, 0.2);
	}

	&.today-card {
		background: linear-gradient(135deg, rgba($accent, 0.1) 0%, rgba($accent, 0.05) 100%);
		border: 2rpx solid rgba($accent, 0.3);
		@include shadow-lg;

		&::before {
			background: linear-gradient(135deg, rgba($accent, 0.08) 0%, transparent 50%);
		}

		&:hover {
			border-color: rgba($accent, 0.4);
		}
	}
}

.history-date {
	@include text-xl;
	@include font-bold;
	color: $primary-solid;
	margin-bottom: 8rpx;
	@include flex;
	@include items-center;
	@include justify-between;
	position: relative;
}

.today-tag {
	background: linear-gradient(135deg, $accent 0%, lighten($accent, 10%) 100%);
	color: $white;
	@include text-xs;
	@include font-bold;
	padding: 6rpx 16rpx;
	border-radius: $radius-xl;
	@include shadow-sm;
	position: relative;

	&::before {
		content: "";
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(135deg, rgba($white, 0.2) 0%, transparent 50%);
		border-radius: $radius-xl;
		pointer-events: none;
	}
}

.history-summary {
	@include text-base;
	color: $gray-700;
	margin-bottom: 8rpx;
	line-height: 1.5;
}

.history-remark {
	@include text-sm;
	@include font-medium;
	color: $primary-solid;
	background: linear-gradient(135deg, rgba($primary-solid, 0.1) 0%, rgba($primary-solid, 0.05) 100%);
	padding: 12rpx 16rpx;
	border-radius: $radius-md;
	border: 1rpx solid rgba($primary-solid, 0.2);
	margin-top: 8rpx;
	word-break: break-all;
	line-height: 1.4;
	@include flex;
	@include items-center;
	@include gap-2;
}

/* 菜品清单样式 */
.history-dishes {
	margin-top: 24rpx;
	padding-top: 24rpx;
	border-top: 2rpx solid rgba($primary-solid, 0.15);
}

.dishes-title {
	@include text-sm;
	@include font-semibold;
	color: $gray-700;
	margin-bottom: 16rpx;
	@include flex;
	@include items-center;
	@include gap-2;

	&::before {
		content: "🍽️";
		font-size: 24rpx;
	}
}

.dishes-list {
	@include flex;
	flex-wrap: wrap;
	gap: 16rpx;
}

.dish-item {
	background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(139, 92, 246, 0.08) 100%);
	color: $primary-solid;
	padding: 12rpx 20rpx;
	border-radius: $radius-lg;
	font-size: 26rpx;
	font-weight: 500;
	border: 2rpx solid rgba($primary-solid, 0.2);
	white-space: nowrap;
	@include shadow-sm;
	@include transition;
	backdrop-filter: blur(4rpx);
	position: relative;

	&::before {
		content: "";
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(135deg, rgba($white, 0.1) 0%, transparent 50%);
		border-radius: $radius-lg;
		pointer-events: none;
	}

	&:hover {
		transform: translateY(-1rpx);
		@include shadow-md;
		border-color: rgba($primary-solid, 0.3);
	}
}

.back-btn {
	@include modern-btn;
	@include btn-primary;
	@include flex;
	@include items-center;
	@include justify-center;
	@include gap-2;
	@include font-bold;
	@include text-base;
	color: $white;
	background: linear-gradient(135deg, $primary-solid 0%, lighten($primary-solid, 10%) 100%);
	padding: 16rpx 32rpx;
	border-radius: $radius-xl;
	@include shadow-lg;
	position: fixed;
	right: 32rpx;
	bottom: 32rpx;
	z-index: 100;
	min-width: 160rpx;
	@include transition;

	&:hover {
		transform: translateY(-2rpx);
		@include shadow-xl;
	}

	&:active {
		transform: translateY(0);
	}
}
