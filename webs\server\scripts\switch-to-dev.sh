#!/bin/bash
# 切换回开发环境脚本

echo "🔄 切换回开发环境..."

# 检查是否在正确的目录
if [ ! -f "package.json" ]; then
    echo "❌ 错误: 请在 webs/server 目录下运行此脚本"
    exit 1
fi

# 1. 恢复 SQLite schema
echo "🔄 恢复 SQLite 数据库配置..."
if [ -f "prisma/schema.sqlite.backup" ]; then
    cp prisma/schema.sqlite.backup prisma/schema.prisma
    echo "✅ 已恢复 SQLite schema"
else
    echo "⚠️ 警告: 没有找到 SQLite schema 备份文件"
fi

# 2. 切换到开发环境配置
echo "⚙️ 配置开发环境..."
if [ -f ".env.development" ]; then
    cp .env.development .env
    echo "✅ 已切换到开发环境配置"
else
    echo "⚠️ 警告: .env.development 文件不存在，使用默认 .env"
fi

# 3. 重新生成 Prisma 客户端
echo "🔧 重新生成 Prisma 客户端..."
npm run generate

echo ""
echo "✅ 已切换回开发环境!"
echo "🗄️ 数据库: SQLite (本地文件)"
echo "🔧 启动开发服务: npm run dev"
