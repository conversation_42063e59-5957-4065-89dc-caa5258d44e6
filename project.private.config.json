{"description": "项目私有配置文件。此文件中的内容将覆盖 project.config.json 中的相同字段。项目的改动优先同步到此文件中。详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html", "projectname": "nanan", "setting": {"compileHotReLoad": true, "urlCheck": false, "bigPackageSizeSupport": true, "autoAudits": false, "coverView": true, "lazyloadPlaceholderEnable": false, "preloadBackgroundData": false, "useApiHook": true, "useApiHostProcess": true, "showShadowRootInWxmlPanel": true, "useStaticServer": false, "useLanDebug": false, "showES6CompileOption": false, "checkInvalidKey": true, "ignoreDevUnusedFiles": true}, "libVersion": "3.8.11", "condition": {"miniprogram": {"list": [{"name": "pages/subscribe-test/index", "pathName": "pages/subscribe-test/index", "query": "", "scene": null, "launchMode": "default"}, {"name": "新增菜品", "pathName": "pages/add_menu/index", "query": "", "launchMode": "default", "scene": null}, {"name": "留言", "pathName": "pages/family_message/index", "query": "", "launchMode": "default", "scene": null}, {"name": "用户关联", "pathName": "pages/user_connection/index", "query": "", "launchMode": "default", "scene": null}, {"name": "个人信息", "pathName": "pages/user_profile/index", "query": "", "launchMode": "default", "scene": null}, {"name": "我的", "pathName": "pages/mine/index", "query": "", "launchMode": "default", "scene": null}, {"name": "新增菜品", "pathName": "pages/dish/add/index", "query": "", "launchMode": "default", "scene": null}, {"name": "我的菜品", "pathName": "pages/dish/my-dishes/index", "query": "", "launchMode": "default", "scene": null}, {"name": "点菜页面", "pathName": "pages/order/index", "query": "", "launchMode": "default", "scene": null}]}}, "ignore": ["webs/admin/**/*"]}