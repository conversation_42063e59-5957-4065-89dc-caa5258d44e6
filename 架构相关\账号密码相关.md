云服务器系统：Ubuntu 24.04 LTS  （这个才有node高版本）
 
 【云服务器】请在安全组放行 25814 端口
 外网面板地址: https://*************:25814/7b833aa8
 内网面板地址: https://*************:25814/7b833aa8
 username: omsfoypg
 password: 46c32672
 
 安装：mysql-8.4.x 兼容类型
 安装：node.js18.2
 安装：nginx

 - **公网IP**: *************
- **域名**: www.huanglun.asia (已解析)
- **宝塔面板**: 免费版 9.6.0

创建数据库
```
1. 数据库 → 添加数据库
2. 数据库名: nannan_db
3. 用户名: nannan_user
4. 密码: 5201314hl 
端口号：3306

#### 3.2 部署服务
```
终端执行:
cd /www/wwwroot/www.huanglun.asia/api/server
npm run deploy:prod

我的github账号：24..
密码tokens登录：*********************************************************************************************
公约：SHA256:xNKVF1Y+d45OUTp46IO9IIw2gwZBi5m895sm+eLpxCs 

ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQDorVB1FYyTGgJ09GrELZPzghLc2mgozs/qtBUS9Ou5llRSjniq2qLRDvWAV/CsXqamelneDtjGEZjOOAS+FZKCF6RylExlmHBs8WYIP/5XzFa7AKski+OGdtKt9t2oSdQ7m0HUm/AEtCB6ZwAjblgk2BDFEBggQVdHcXPmx6n6v83MDgPuPCXBEvweg+Irx9W457YeAW01nhhquewQtEEziz6T9ZUTVBpBjhWf16qln+a0kx0Nx6lriCcC2wrK9EHiA/+DG/sEh6/4dMFyqbPegpMInGF7kzIjQl3uWz+4TXyvBW9ujzvM2VA2je/WUBK+exgwmP0abVjptJnhWu5toixMNLclmX08w6QRTaE9FUz1eWn1xiwIRolz0lI28BlQ92SO1xvEwpfbVnrAkvhyRTAtb8zM9J9q8rOJ9nNV3Vtczf0MH/crJa8pZZCSskSsZ6KYlx4oVSNVd8pRfau0nU/WQmu7u+HzafAJL3OUHFy692u9oMb+8hT8BpmOZi8= root@iZ7xvbwlbyn3mlc4why2htZ
```


 
L:
www.huanglun.asia

L:
https://*************:20198/


图片管理 ：
https://picx.xpoet.cn/#/management

本地数据库：http://localhost:5555/


node部署到云服务器的逻辑：https://blog.csdn.net/Leijiang0504/article/details/129620483

常用命令： 
pm2 
 pm2 restart node-server
 bash deploy.sh
 pm2 logs nannan-api --lines 30