require('dotenv').config();
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function finalCleanup() {
  try {
    console.log('🔍 最终清理 - 清空所有数据表（除了张三和李四的账号）...\n');
    
    // 1. 先检查张三和李四的ID
    const zhangsan = await prisma.user.findFirst({ where: { name: '张三' } });
    const lisi = await prisma.user.findFirst({ where: { name: '李四' } });
    
    if (!zhangsan || !lisi) {
      console.log('❌ 未找到张三或李四');
      return;
    }
    
    const keepUserIds = [zhangsan.id, lisi.id];
    console.log('保留用户ID:', keepUserIds);
    console.log(`保留用户: ${zhangsan.name}, ${lisi.name}\n`);
    
    // 2. 按照外键依赖关系的顺序删除所有数据
    console.log('🧹 开始最终清理...');
    
    // 删除推荐菜单
    console.log('1️⃣ 清空推荐菜单表...');
    const deletedRecommendedMenus = await prisma.recommendedMenu.deleteMany({});
    console.log(`   删除了 ${deletedRecommendedMenus.count} 条记录`);
    
    // 删除订单推送
    console.log('2️⃣ 清空订单推送表...');
    const deletedOrderPushes = await prisma.orderPush.deleteMany({});
    console.log(`   删除了 ${deletedOrderPushes.count} 条记录`);
    
    // 删除留言接收者
    console.log('3️⃣ 清空留言接收者表...');
    const deletedMessageRecipients = await prisma.messageRecipient.deleteMany({});
    console.log(`   删除了 ${deletedMessageRecipients.count} 条记录`);
    
    // 删除用户连接
    console.log('4️⃣ 清空用户连接表...');
    const deletedConnections = await prisma.userConnection.deleteMany({});
    console.log(`   删除了 ${deletedConnections.count} 条记录`);
    
    // 删除通知
    console.log('5️⃣ 清空通知表...');
    const deletedNotifications = await prisma.notification.deleteMany({});
    console.log(`   删除了 ${deletedNotifications.count} 条记录`);
    
    // 删除留言
    console.log('6️⃣ 清空留言表...');
    const deletedMessages = await prisma.message.deleteMany({});
    console.log(`   删除了 ${deletedMessages.count} 条记录`);
    
    // 删除订单
    console.log('7️⃣ 清空订单表...');
    const deletedOrders = await prisma.order.deleteMany({});
    console.log(`   删除了 ${deletedOrders.count} 条记录`);
    
    // 删除菜单项
    console.log('8️⃣ 清空菜单项表...');
    const deletedMenuItems = await prisma.menuItem.deleteMany({});
    console.log(`   删除了 ${deletedMenuItems.count} 条记录`);
    
    // 删除菜单
    console.log('9️⃣ 清空菜单表...');
    const deletedMenus = await prisma.menu.deleteMany({});
    console.log(`   删除了 ${deletedMenus.count} 条记录`);
    
    // 删除菜品
    console.log('🔟 清空菜品表...');
    const deletedDishes = await prisma.dish.deleteMany({});
    console.log(`   删除了 ${deletedDishes.count} 条记录`);
    
    // 删除分类
    console.log('1️⃣1️⃣ 清空分类表...');
    const deletedCategories = await prisma.category.deleteMany({});
    console.log(`   删除了 ${deletedCategories.count} 条记录`);
    
    // 删除其他用户（保留张三和李四）
    console.log('1️⃣2️⃣ 清空其他用户...');
    const deletedUsers = await prisma.user.deleteMany({
      where: { id: { notIn: keepUserIds } }
    });
    console.log(`   删除了 ${deletedUsers.count} 个用户`);
    
    console.log('\n✅ 最终清理完成！');
    
    // 3. 验证结果
    console.log('\n🔍 验证清理结果...');
    const [
      userCount,
      dishCount,
      menuCount,
      menuItemCount,
      orderCount,
      messageCount,
      messageRecipientCount,
      notificationCount,
      connectionCount,
      orderPushCount,
      recommendedMenuCount,
      categoryCount
    ] = await Promise.all([
      prisma.user.count(),
      prisma.dish.count(),
      prisma.menu.count(),
      prisma.menuItem.count(),
      prisma.order.count(),
      prisma.message.count(),
      prisma.messageRecipient.count(),
      prisma.notification.count(),
      prisma.userConnection.count(),
      prisma.orderPush.count(),
      prisma.recommendedMenu.count(),
      prisma.category.count()
    ]);
    
    console.log('📊 清理后数据统计:');
    console.log(`  用户 (User): ${userCount} 条`);
    console.log(`  菜品 (Dish): ${dishCount} 条`);
    console.log(`  菜单 (Menu): ${menuCount} 条`);
    console.log(`  菜单项 (MenuItem): ${menuItemCount} 条`);
    console.log(`  订单 (Order): ${orderCount} 条`);
    console.log(`  留言 (Message): ${messageCount} 条`);
    console.log(`  留言接收者 (MessageRecipient): ${messageRecipientCount} 条`);
    console.log(`  通知 (Notification): ${notificationCount} 条`);
    console.log(`  用户连接 (UserConnection): ${connectionCount} 条`);
    console.log(`  订单推送 (OrderPush): ${orderPushCount} 条`);
    console.log(`  推荐菜单 (RecommendedMenu): ${recommendedMenuCount} 条`);
    console.log(`  分类 (Category): ${categoryCount} 条`);
    
    // 显示剩余用户
    const remainingUsers = await prisma.user.findMany({
      select: { id: true, name: true, phone: true }
    });
    
    console.log('\n👥 剩余用户:');
    remainingUsers.forEach(user => {
      console.log(`  ✅ ${user.name} (${user.phone})`);
    });
    
    // 检查是否完全清理
    const totalDataCount = dishCount + menuCount + menuItemCount + orderCount + 
                          messageCount + messageRecipientCount + notificationCount + 
                          connectionCount + orderPushCount + recommendedMenuCount + categoryCount;
    
    if (totalDataCount === 0 && userCount === 2) {
      console.log('\n🎉 数据库已完全清理！只保留张三和李四的账号。');
    } else {
      console.log('\n⚠️  数据库清理完成，但可能还有一些数据残留。');
    }
    
    return {
      deletedRecommendedMenus: deletedRecommendedMenus.count,
      deletedOrderPushes: deletedOrderPushes.count,
      deletedMessageRecipients: deletedMessageRecipients.count,
      deletedConnections: deletedConnections.count,
      deletedNotifications: deletedNotifications.count,
      deletedMessages: deletedMessages.count,
      deletedOrders: deletedOrders.count,
      deletedMenuItems: deletedMenuItems.count,
      deletedMenus: deletedMenus.count,
      deletedDishes: deletedDishes.count,
      deletedCategories: deletedCategories.count,
      deletedUsers: deletedUsers.count,
      finalCounts: {
        userCount, dishCount, menuCount, menuItemCount, orderCount,
        messageCount, messageRecipientCount, notificationCount,
        connectionCount, orderPushCount, recommendedMenuCount, categoryCount
      }
    };
    
  } catch (error) {
    console.error('❌ 最终清理失败:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

finalCleanup();
