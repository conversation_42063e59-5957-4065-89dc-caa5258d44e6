const crypto = require('crypto');

/**
 * 微信小程序消息推送回调控制器
 */

/**
 * 验证微信服务器签名
 */
function verifySignature(signature, timestamp, nonce, token) {
  try {
    const tmpArr = [token, timestamp, nonce];
    tmpArr.sort();
    const tmpStr = tmpArr.join('');
    const hashCode = crypto.createHash('sha1').update(tmpStr).digest('hex');
    return hashCode === signature;
  } catch (error) {
    console.error('签名验证失败:', error);
    return false;
  }
}

/**
 * 微信服务器验证
 * @route GET /api/wechat/callback
 */
const verifyServer = async (req, res) => {
  try {
    const { signature, timestamp, nonce, echostr } = req.query;
    const token = process.env.WECHAT_MP_TOKEN || 'wx_token_2024';

    console.log('🔍 微信服务器验证请求:', { 
      signature, 
      timestamp, 
      nonce, 
      echostr: echostr ? echostr.substring(0, 10) + '...' : 'none'
    });

    // 验证签名
    const isValid = verifySignature(signature, timestamp, nonce, token);

    if (isValid) {
      console.log('✅ 微信服务器验证成功');
      res.send(echostr);
    } else {
      console.log('❌ 微信服务器验证失败');
      res.status(403).send('Forbidden');
    }
  } catch (err) {
    console.error('微信服务器验证异常:', err);
    res.status(500).send('Internal Server Error');
  }
};

/**
 * 处理微信推送消息
 * @route POST /api/wechat/callback
 */
const handleMessage = async (req, res) => {
  try {
    const { signature, timestamp, nonce } = req.query;
    const token = process.env.WECHAT_MP_TOKEN || 'wx_token_2024';

    console.log('📨 收到微信推送消息');

    // 验证签名
    const isValid = verifySignature(signature, timestamp, nonce, token);
    if (!isValid) {
      console.log('❌ 消息签名验证失败');
      return res.status(403).send('Forbidden');
    }

    console.log('📋 消息内容:', req.body);

    // 返回success给微信服务器
    res.send('success');
  } catch (err) {
    console.error('处理微信消息异常:', err);
    res.status(200).send('success');
  }
};

module.exports = {
  verifyServer,
  handleMessage
};
