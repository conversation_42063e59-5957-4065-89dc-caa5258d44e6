/* 登录页面 - Tailwind CSS 风格 */

/* 确保容器样式正确应用 */
.container {
  min-height: 100vh;
  padding: 32rpx;
  padding-top: 120rpx;
  background-color: #f9fafb;
}

/* 标题样式增强 */
.text-2xl.font-bold.text-gray-900 {
  font-size: 48rpx;
  font-weight: 700;
  color: #111827;
  line-height: 1.2;
}

.text-sm.text-gray-500 {
  font-size: 26rpx;
  color: #6b7280;
}

/* 卡片样式增强 */
.card {
  background: white;
  border-radius: 16rpx;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  padding: 32rpx;
  margin-bottom: 24rpx;
}

/* Tab 切换样式 */
.tab-bar {
  display: flex;
  background: #f3f4f6;
  border-radius: 16rpx;
  padding: 8rpx;
  margin-bottom: 32rpx;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 16rpx 12rpx;
  border-radius: 12rpx;
  font-size: 26rpx;
  color: #4b5563;
  transition: all 0.3s ease;
  font-weight: 500;
}

.tab-item.active {
  background-color: #2563eb;
  color: white;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

/* 输入框组样式 */
.input-group {
  margin-bottom: 24rpx;
}

.input-label {
  font-size: 28rpx;
  color: #374151;
  margin-bottom: 12rpx;
  font-weight: 500;
}
/* 按钮样式增强 */
.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx 32rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 600;
  transition: all 0.2s ease;
  border: none;
  position: relative;
  overflow: hidden;
}

.btn-primary {
  background-color: #2563eb;
  color: white;
}

.btn-primary:active {
  background-color: #1d4ed8;
  transform: translateY(1rpx);
}

.btn-primary.opacity-50 {
  opacity: 0.5;
  pointer-events: none;
}

/* 微信登录按钮 */
.bg-green {
  background-color: #10b981 !important;
}

.bg-green:active {
  background-color: #059669 !important;
}

/* 微信头像圆形背景 */
.w-32.h-32.bg-green.rounded-full {
  width: 128rpx;
  height: 128rpx;
  background-color: #10b981;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24rpx;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* 记住密码和忘记密码区域 */
.flex.justify-between.items-center {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.flex.items-center {
  display: flex;
  align-items: center;
}

/* 复选框样式 */
checkbox {
  margin-right: 16rpx;
  /* 修复真机上的黑色背景问题 */
  background-color: transparent !important;
}

/* 复选框内部样式覆盖 */
checkbox .wx-checkbox-input {
  background-color: #ffffff !important;
  border: 2rpx solid #d1d5db !important;
  border-radius: 6rpx !important;
  width: 32rpx !important;
  height: 32rpx !important;
}

/* 选中状态样式 */
checkbox .wx-checkbox-input.wx-checkbox-input-checked {
  background-color: var(--primary-600, #2563eb) !important;
  border-color: var(--primary-600, #2563eb) !important;
}

/* 选中状态的勾号 */
checkbox .wx-checkbox-input.wx-checkbox-input-checked::before {
  color: #ffffff !important;
  font-size: 20rpx !important;
  line-height: 28rpx !important;
  text-align: center !important;
  display: block !important;
}

/* 链接文本样式 */
.text-primary {
  color: #2563eb;
  font-weight: 500;
}

.text-primary:active {
  color: #1d4ed8;
}

/* 错误提示样式 */
.text-red {
  color: #ef4444;
  font-weight: 500;
}

/* 文本居中 */
.text-center {
  text-align: center;
}

/* 间距工具类 */
.mb-2 {
  margin-bottom: 16rpx;
}

.mb-3 {
  margin-bottom: 24rpx;
}

.mb-4 {
  margin-bottom: 32rpx;
}

.mt-3 {
  margin-top: 24rpx;
}

.mt-4 {
  margin-top: 32rpx;
}

.ml-1 {
  margin-left: 8rpx;
}

.ml-2 {
  margin-left: 16rpx;
}

/* 宽度工具类 */
.w-full {
  width: 100%;
}

/* 响应式优化 */
@media (max-width: 750rpx) {
  .container {
    padding: 24rpx;
    padding-top: 100rpx;
  }

  .card {
    padding: 24rpx;
  }

  .tab-item {
    font-size: 24rpx;
    padding: 14rpx 8rpx;
  }
}
