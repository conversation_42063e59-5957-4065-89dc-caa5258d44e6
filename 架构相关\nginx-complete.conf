# Nginx 完整配置文件 - 楠楠家厨项目
# 支持多端口访问，解决域名未备案问题

# 运行用户和进程配置
user  www www;
worker_processes auto;  # 自动检测CPU核心数
error_log  /www/wwwlogs/nginx_error.log  crit;
pid        /www/server/nginx/logs/nginx.pid;
worker_rlimit_nofile 51200;  # 单个进程最大文件打开数

# TCP流处理模块（用于TCP代理）
stream {
    log_format tcp_format '$time_local|$remote_addr|$protocol|$status|$bytes_sent|$bytes_received|$session_time|$upstream_addr|$upstream_bytes_sent|$upstream_bytes_received|$upstream_connect_time';
    access_log /www/wwwlogs/tcp-access.log tcp_format;
    error_log /www/wwwlogs/tcp-error.log;
    include /www/server/panel/vhost/nginx/tcp/*.conf;
}

# 事件处理模块
events {
    use epoll;  # Linux下高效的事件模型
    worker_connections 51200;  # 单个进程最大连接数
    multi_accept on;  # 允许一次接受多个连接
}

# HTTP模块配置
http {
    include       mime.types;
    include proxy.conf;
    lua_package_path "/www/server/nginx/lib/lua/?.lua;;";

    default_type  application/octet-stream;

    # 基础配置
    server_names_hash_bucket_size 512;
    client_header_buffer_size 32k;
    large_client_header_buffers 4 32k;
    client_max_body_size 50m;  # 最大上传文件大小

    # 文件传输优化
    sendfile   on;
    tcp_nopush on;
    keepalive_timeout 60;
    tcp_nodelay on;

    # FastCGI配置（PHP相关）
    fastcgi_connect_timeout 300;
    fastcgi_send_timeout 300;
    fastcgi_read_timeout 300;
    fastcgi_buffer_size 64k;
    fastcgi_buffers 4 64k;
    fastcgi_busy_buffers_size 128k;
    fastcgi_temp_file_write_size 256k;
    fastcgi_intercept_errors on;

    # Gzip压缩配置
    gzip on;
    gzip_min_length  1k;
    gzip_buffers     4 16k;
    gzip_http_version 1.1;
    gzip_comp_level 2;
    gzip_types     text/plain application/javascript application/x-javascript text/javascript text/css application/xml application/json image/jpeg image/gif image/png font/ttf font/otf image/svg+xml application/xml+rss text/x-js;
    gzip_vary on;
    gzip_proxied   expired no-cache no-store private auth;
    gzip_disable   "MSIE [1-6]\.";

    # 连接限制
    limit_conn_zone $binary_remote_addr zone=perip:10m;
    limit_conn_zone $server_name zone=perserver:10m;

    # 安全配置
    server_tokens off;  # 隐藏Nginx版本信息
    access_log off;     # 关闭默认访问日志

    # ===========================================
    # 注意: 已移除phpMyAdmin的30001端口配置
    # 建议使用宝塔面板内置的数据库管理功能，更安全
    # 访问方式: 宝塔面板 → 数据库 → phpMyAdmin
    # ===========================================

    # ===========================================
    # 楠楠家厨 API服务（3001端口）
    # ===========================================
    server {
        listen 3001;
        server_name *************;
        
        # API接口代理到Node.js服务
        location /api {
            proxy_pass http://127.0.0.1:3001;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
            
            # 超时设置
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }
        
        # 部署管理页面
        location /deploy-trigger.php {
            root /www/wwwroot/www.huanglun.asia/api/webs/server;
            index deploy-trigger.php;
            include enable-php.conf;
        }
        
        # Webhook接收
        location /webhook.php {
            root /www/wwwroot/www.huanglun.asia/api/webs/server;
            include enable-php.conf;
        }
        
        # 健康检查直接代理
        location /health {
            proxy_pass http://127.0.0.1:3001/health;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }

        # 日志配置
        access_log /www/wwwlogs/nannan-api.log;
        error_log /www/wwwlogs/nannan-api-error.log;
    }

    # ===========================================
    # 楠楠家厨 管理后台（8080端口）
    # ===========================================
    server {
        listen 8080;
        server_name *************;
        root /www/wwwroot/www.huanglun.asia/admin;
        index index.html;
        
        # Vue.js单页应用配置
        location / {
            try_files $uri $uri/ /index.html;
        }
        
        # 静态资源缓存优化
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header Access-Control-Allow-Origin "*";
        }
        
        # 安全头设置
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";

        # 日志配置
        access_log /www/wwwlogs/nannan-admin.log;
        error_log /www/wwwlogs/nannan-admin-error.log;
    }

    # ===========================================
    # 默认80端口（备用，域名备案后使用）
    # ===========================================
    server {
        listen 80;
        server_name www.huanglun.asia huanglun.asia *************;
        
        # 临时重定向到IP端口访问
        location / {
            return 302 http://*************:8080;
        }
        
        # API重定向
        location /api {
            return 302 http://*************:3001$request_uri;
        }
    }

    # 包含其他站点配置文件
    include /www/server/panel/vhost/nginx/*.conf;
}
