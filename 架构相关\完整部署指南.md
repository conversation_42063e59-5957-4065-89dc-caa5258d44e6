# 🚀 楠楠家厨 - 完整部署指南

## 📋 服务器信息
- **云服务器**: 阿里云ECS 2核2GB Ubuntu 22.04 LTS
- **公网IP**: *************
- **域名**: www.huanglun.asia (已解析)
- **宝塔面板**: 最新版

## 🎯 完整部署流程

### 阶段1: 服务器环境准备

#### 1.1 宝塔面板安装
```bash
# Ubuntu系统安装宝塔面板
wget -O install.sh https://download.bt.cn/install/install-ubuntu_6.0.sh
chmod +x install.sh
sudo bash install.sh ed8484bec
```

#### 1.2 安装LNMP环境
宝塔面板首次登录时选择安装：
- **Nginx**: 最新版
- **MySQL**: 5.7或8.0
- **PHP**: 7.4或8.0（可选）
- **Node.js**: 18.x或20.x

#### 1.3 创建网站
```
宝塔面板 → 网站 → 添加站点
- 域名: www.huanglun.asia
- 根目录: /www/wwwroot/www.huanglun.asia
- PHP版本: 纯静态
- 数据库: 不创建
```

### 阶段2: 数据库配置

#### 2.1 创建MySQL数据库
```
宝塔面板 → 数据库 → 添加数据库
- 数据库名: nannan_db
- 用户名: nannan_user
- 密码: 5201314hl
- 字符集: utf8mb4
- 访问权限: 本地服务器
```

#### 2.2 验证数据库连接
```bash
# 测试数据库连接
mysql -u nannan_user -p5201314hl -h localhost nannan_db
```

### 阶段3: 后端部署

#### 3.1 上传后端代码
```bash
# 方式1: 宝塔面板文件管理
1. 将 webs/server 打包成 server.zip
2. 上传到 /www/wwwroot/www.huanglun.asia/api/
3. 解压缩

# 方式2: SCP命令上传
scp -r webs/server/ root@*************:/www/wwwroot/www.huanglun.asia/api/
```

#### 3.2 安装依赖和部署
```bash
# 进入项目目录
cd /www/wwwroot/www.huanglun.asia/api

# 安装依赖
npm install

# 生成Prisma客户端
npx prisma generate

# 同步数据库结构
npx prisma db push

# 安装PM2
npm install -g pm2

# 启动服务
pm2 start src/app.js --name nannan-api --env production

# 保存PM2配置
pm2 save
pm2 startup
```

#### 3.3 验证后端服务
```bash
# 查看服务状态
pm2 status

# 查看日志
pm2 logs nannan-api

# 测试API
curl http://localhost:3001/api/health
```

### 阶段4: 前端部署

#### 4.1 本地打包管理后台
```bash
# 在开发电脑上执行
cd webs/admin

# 安装依赖
npm install

# 打包生产版本
npm run build

# 会生成 dist 文件夹
```

#### 4.2 上传前端文件
```bash
# 方式1: 宝塔面板文件管理
1. 将 dist 文件夹内容打包成 admin.zip
2. 上传到 /www/wwwroot/www.huanglun.asia/admin/
3. 解压缩

# 方式2: SCP命令上传
scp -r webs/admin/dist/* root@*************:/www/wwwroot/www.huanglun.asia/admin/
```

#### 4.3 配置静态文件访问
```
宝塔面板 → 网站 → www.huanglun.asia → 设置
→ 网站目录 → 运行目录: /admin
→ 保存
```

### 阶段5: Nginx配置

#### 5.1 配置反向代理
```
宝塔面板 → 网站 → www.huanglun.asia → 设置 → 反向代理
→ 添加反向代理:
  - 代理名称: api
  - 目标URL: http://127.0.0.1:3001
  - 发送域名: $host
  - 代理目录: /api
→ 提交
```

#### 5.2 配置SSL证书（域名备案后）
```
网站设置 → SSL → Let's Encrypt
- 域名: www.huanglun.asia
- 邮箱: 您的邮箱
- 申请证书
- 开启强制HTTPS
```

### 阶段6: 测试验证

#### 6.1 测试访问地址
```
# 管理后台
http://www.huanglun.asia/admin

# API接口
http://www.huanglun.asia/api/health

# 数据库连接测试
http://www.huanglun.asia/api/test-db
```

#### 6.2 功能测试
1. 管理后台登录
2. 菜品管理功能
3. 订单管理功能
4. 微信小程序API调用

## 🔧 维护命令

### PM2进程管理
```bash
# 查看服务状态
pm2 status

# 重启服务
pm2 restart nannan-api

# 查看日志
pm2 logs nannan-api

# 停止服务
pm2 stop nannan-api
```

### 数据库管理
```bash
# 备份数据库
mysqldump -u nannan_user -p5201314hl nannan_db > backup.sql

# 恢复数据库
mysql -u nannan_user -p5201314hl nannan_db < backup.sql
```

### 更新部署
```bash
# 1. 上传新代码
# 2. 重新安装依赖
cd /www/wwwroot/www.huanglun.asia/api
npm install

# 3. 更新数据库
npx prisma db push

# 4. 重启服务
pm2 restart nannan-api
```

## 🚨 故障排查

### 常见问题
1. **API无法访问**: 检查PM2服务状态
2. **数据库连接失败**: 检查MySQL服务和连接字符串
3. **前端页面空白**: 检查静态文件路径和Nginx配置
4. **端口冲突**: 检查3001端口是否被占用

### 日志查看
```bash
# PM2日志
pm2 logs nannan-api

# Nginx日志
tail -f /www/wwwlogs/www.huanglun.asia.log

# MySQL日志
tail -f /var/log/mysql/error.log
```

## 🎉 部署完成

部署成功后，您将拥有：
- ✅ 完整的后端API服务
- ✅ 管理后台界面
- ✅ 数据库管理系统
- ✅ 自动化进程管理
- ✅ 反向代理配置

接下来可以：
1. 配置微信小程序API地址
2. 测试完整业务流程
3. 设置定时备份
4. 监控服务状态

 使用流程
日常开发流程：
本地开发 → 修改代码
Git提交 → git add . && git commit -m "更新功能"
推送代码 → git push origin main
自动部署 → 访问 http://www.huanglun.asia/deploy 点击部署按钮
或者设置GitHub Webhook实现完全自动化：
每次push代码后自动部署，无需手动操作