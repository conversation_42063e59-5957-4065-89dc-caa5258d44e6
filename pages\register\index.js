const { userApi } = require('../../services/api');

Page({
  data: {
    form: {
      username: '',
      phone: '',
      password: '',
      confirmPassword: ''
    },
    showPassword: false,
    showConfirmPassword: false,
    registering: false,
    tipMessage: '',
    tipType: 'info' // info, error, success
  },

  computed: {
    canRegister() {
      const { username, phone, password, confirmPassword } = this.data.form;
      return username.length >= 2 && 
             phone.length === 11 && 
             password.length >= 6 && 
             password === confirmPassword;
    }
  },

  onLoad() {
    // 页面加载时的初始化
  },

  // 输入事件处理
  onUsernameChange(e) {
    this.setData({
      'form.username': e.detail.value,
      tipMessage: ''
    });
  },

  onPhoneChange(e) {
    this.setData({
      'form.phone': e.detail.value,
      tipMessage: ''
    });
  },

  onPasswordChange(e) {
    this.setData({
      'form.password': e.detail.value,
      tipMessage: ''
    });
  },

  onConfirmPasswordChange(e) {
    this.setData({
      'form.confirmPassword': e.detail.value,
      tipMessage: ''
    });
  },

  // 切换密码显示
  togglePassword() {
    this.setData({
      showPassword: !this.data.showPassword
    });
  },

  toggleConfirmPassword() {
    this.setData({
      showConfirmPassword: !this.data.showConfirmPassword
    });
  },

  // 表单验证
  validateForm() {
    const { username, phone, password, confirmPassword } = this.data.form;

    if (!username || username.length < 2) {
      this.showTip('用户名至少需要2个字符', 'error');
      return false;
    }

    if (!phone || !/^1[3-9]\d{9}$/.test(phone)) {
      this.showTip('请输入正确的手机号', 'error');
      return false;
    }

    if (!password || password.length < 6) {
      this.showTip('密码至少需要6个字符', 'error');
      return false;
    }

    if (password !== confirmPassword) {
      this.showTip('两次输入的密码不一致', 'error');
      return false;
    }

    return true;
  },

  // 显示提示信息
  showTip(message, type = 'info') {
    this.setData({
      tipMessage: message,
      tipType: type
    });

    // 3秒后自动清除提示
    setTimeout(() => {
      this.setData({
        tipMessage: ''
      });
    }, 3000);
  },

  // 注册处理
  async handleRegister() {
    if (!this.validateForm()) {
      return;
    }

    this.setData({ registering: true });

    try {
      const { username, phone, password } = this.data.form;
      
      const response = await userApi.register({
        name: username,
        phone: phone,
        password: password
      });

      if (response.code === 200) {
        this.showTip('注册成功！', 'success');
        
        // 延迟跳转到登录页面
        setTimeout(() => {
          wx.redirectTo({
            url: '/pages/login/index?phone=' + phone
          });
        }, 1500);
      } else {
        this.showTip(response.message || '注册失败，请重试', 'error');
      }
    } catch (error) {
      console.error('注册失败:', error);
      this.showTip(error.message || '网络错误，请重试', 'error');
    } finally {
      this.setData({ registering: false });
    }
  },

  // 微信注册
  async handleWechatRegister(e) {
    if (!e.detail.userInfo) {
      this.showTip('需要授权才能使用微信注册', 'error');
      return;
    }

    this.setData({ registering: true });

    try {
      // 获取微信登录code
      const loginRes = await wx.login();
      
      const response = await userApi.wechatRegister({
        code: loginRes.code,
        userInfo: e.detail.userInfo
      });

      if (response.code === 200) {
        this.showTip('微信注册成功！', 'success');
        
        // 保存用户信息
        wx.setStorageSync('token', response.data.token);
        wx.setStorageSync('userInfo', response.data.user);
        
        // 跳转到首页
        setTimeout(() => {
          wx.switchTab({
            url: '/pages/home/<USER>'
          });
        }, 1500);
      } else {
        this.showTip(response.message || '微信注册失败', 'error');
      }
    } catch (error) {
      console.error('微信注册失败:', error);
      this.showTip('微信注册失败，请重试', 'error');
    } finally {
      this.setData({ registering: false });
    }
  },

  // 跳转到登录页面
  goToLogin() {
    wx.redirectTo({
      url: '/pages/login/index'
    });
  }
});
