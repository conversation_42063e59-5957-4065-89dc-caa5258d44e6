const {
  formatTime,
  formatRelativeTime,
  formatMealTime,
  formatDate
} = require('../../utils/timeUtils');

Page({
  data: {
    order: null,
    loading: true
  },

  onLoad() {
    this.loadOrderDetail();
  },

  // 加载订单详情
  loadOrderDetail() {
    try {
      const orderDetail = wx.getStorageSync('selectedOrderDetail');

      if (orderDetail) {
        console.log('📋 原始订单数据:', orderDetail);
        console.log('🕒 时间字段检查:', {
          createdAt: orderDetail.createdAt,
          updatedAt: orderDetail.updatedAt,
          diningTime: orderDetail.diningTime,
          menuDate: orderDetail.menu?.date
        });

        // 处理订单数据
        const processedOrder = {
          ...orderDetail,
          items: Array.isArray(orderDetail.items)
            ? orderDetail.items
            : JSON.parse(orderDetail.items || '[]'),
          // 如果已经有格式化的时间字段，直接使用；否则重新格式化
          createdAtFormatted:
            orderDetail.createdAtFormatted || formatTime(orderDetail.createdAt),
          diningTimeFormatted:
            orderDetail.diningTimeFormatted ||
            (orderDetail.diningTime
              ? formatTime(orderDetail.diningTime)
              : null),
          updatedAtFormatted:
            orderDetail.updatedAtFormatted || formatTime(orderDetail.updatedAt),
          // 处理菜单时间
          menu: orderDetail.menu
            ? {
                ...orderDetail.menu,
                dateFormatted:
                  orderDetail.menu.dateFormatted ||
                  formatDate(orderDetail.menu.date)
              }
            : null
        };

        console.log('✅ 格式化后的时间:', {
          createdAtFormatted: processedOrder.createdAtFormatted,
          updatedAtFormatted: processedOrder.updatedAtFormatted,
          diningTimeFormatted: processedOrder.diningTimeFormatted,
          menuDateFormatted: processedOrder.menu?.dateFormatted
        });

        this.setData({
          order: processedOrder,
          loading: false
        });
      } else {
        wx.showToast({
          title: '订单数据不存在',
          icon: 'none'
        });

        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    } catch (error) {
      console.error('加载订单详情失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });

      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 获取订单状态文本
  getStatusText(status) {
    const statusMap = {
      pending: '待处理',
      confirmed: '已确认',
      preparing: '准备中',
      ready: '已完成',
      cancelled: '已取消',
      pushed: '已推送'
    };
    return statusMap[status] || status;
  },

  // 获取订单状态颜色
  getStatusColor(status) {
    const colorMap = {
      pending: '#ff9500',
      confirmed: '#1989fa',
      preparing: '#ff9500',
      ready: '#07c160',
      cancelled: '#ee0a24',
      pushed: '#1989fa'
    };
    return colorMap[status] || '#666';
  },

  // 返回上一页
  goBack() {
    wx.navigateBack();
  },

  // 联系推送者（如果是被推送的订单）
  contactPusher() {
    const {order} = this.data;

    if (order.isPushedToMe && order.pushedBy) {
      wx.showModal({
        title: '联系推送者',
        content: `联系 ${order.pushedBy.name}？`,
        confirmText: '确定',
        cancelText: '取消',
        success: res => {
          if (res.confirm) {
            // 这里可以添加联系功能，比如跳转到聊天页面
            wx.showToast({
              title: '功能开发中',
              icon: 'none'
            });
          }
        }
      });
    }
  },

  // 分享订单
  onShareAppMessage() {
    const {order} = this.data;

    return {
      title: `订单详情 - ${order.menu?.remark || '菜单'}`,
      path: `/pages/order_detail/index`,
      imageUrl: order.items?.[0]?.image || ''
    };
  }
});
