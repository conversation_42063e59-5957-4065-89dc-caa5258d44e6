require('dotenv').config();
const {PrismaClient} = require('@prisma/client');

const prisma = new PrismaClient();

async function checkCategories() {
  try {
    console.log('🔍 检查分类表数据...');
    const categories = await prisma.category.findMany({
      select: {id: true, name: true, createdAt: true}
    });

    console.log('📋 分类列表:');
    categories.forEach((cat, index) => {
      console.log(
        `  ${index + 1}. ${
          cat.name
        } (创建时间: ${cat.createdAt.toLocaleString()})`
      );
    });

    console.log(`\n总计: ${categories.length} 个分类`);
  } catch (error) {
    console.error('❌ 检查失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkCategories();
