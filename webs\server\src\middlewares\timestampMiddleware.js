/**
 * 时间戳中间件
 * 统一处理创建时间和更新时间
 */

/**
 * 为创建操作添加时间戳
 * 在请求体中添加 createdAt 和 updatedAt 字段
 */
const addCreateTimestamp = (req, res, next) => {
  const now = new Date();

  // 如果请求体存在且是对象
  if (req.body && typeof req.body === 'object') {
    // 确保创建时间存在
    if (!req.body.createdAt) {
      req.body.createdAt = now;
    }

    // 确保更新时间存在
    if (!req.body.updatedAt) {
      req.body.updatedAt = now;
    }
  }

  console.log(`📅 [CREATE] 添加时间戳: ${now.toISOString()}`);
  next();
};

/**
 * 为更新操作添加时间戳
 * 在请求体中添加或更新 updatedAt 字段
 */
const addUpdateTimestamp = (req, res, next) => {
  const now = new Date();

  // 如果请求体存在且是对象
  if (req.body && typeof req.body === 'object') {
    // 强制更新时间
    req.body.updatedAt = now;
  }

  console.log(`📅 [UPDATE] 更新时间戳: ${now.toISOString()}`);
  next();
};

/**
 * 通用时间戳中间件
 * 根据请求方法自动选择合适的时间戳处理
 */
const autoTimestamp = (req, res, next) => {
  const method = req.method.toLowerCase();
  const now = new Date();

  // 如果请求体存在且是对象
  if (req.body && typeof req.body === 'object') {
    switch (method) {
      case 'post':
        // 创建操作：添加创建时间和更新时间
        if (!req.body.createdAt) {
          req.body.createdAt = now;
        }
        if (!req.body.updatedAt) {
          req.body.updatedAt = now;
        }
        console.log(`📅 [AUTO-CREATE] 添加时间戳: ${now.toISOString()}`);
        break;

      case 'put':
      case 'patch':
        // 更新操作：只更新更新时间
        req.body.updatedAt = now;
        console.log(`📅 [AUTO-UPDATE] 更新时间戳: ${now.toISOString()}`);
        break;

      default:
        // 其他操作不处理时间戳
        break;
    }
  }

  next();
};

/**
 * Prisma 查询增强中间件
 * 在 Prisma 查询中自动添加时间戳字段
 */
const enhancePrismaQuery = (req, res, next) => {
  // 保存原始的 Prisma 实例（如果需要的话）
  const originalPrisma = req.prisma;

  // 为请求添加时间戳工具函数
  req.addTimestamps = {
    /**
     * 为创建数据添加时间戳
     * @param {Object} data - 要创建的数据
     * @returns {Object} - 添加了时间戳的数据
     */
    forCreate: data => {
      const now = new Date();
      return {
        ...data,
        createdAt: data.createdAt || now,
        updatedAt: data.updatedAt || now
      };
    },

    /**
     * 为更新数据添加时间戳
     * @param {Object} data - 要更新的数据
     * @returns {Object} - 添加了时间戳的数据
     */
    forUpdate: data => {
      const now = new Date();
      return {
        ...data,
        updatedAt: now
      };
    },

    /**
     * 获取当前时间戳
     * @returns {Date} - 当前时间
     */
    now: () => new Date()
  };

  next();
};

/**
 * 验证时间戳格式的中间件
 */
const validateTimestamps = (req, res, next) => {
  if (req.body && typeof req.body === 'object') {
    const {createdAt, updatedAt} = req.body;

    // 验证 createdAt 格式
    if (
      createdAt &&
      !(createdAt instanceof Date) &&
      isNaN(Date.parse(createdAt))
    ) {
      return res.status(400).json({
        code: 400,
        message: 'Invalid createdAt format',
        data: null
      });
    }

    // 验证 updatedAt 格式
    if (
      updatedAt &&
      !(updatedAt instanceof Date) &&
      isNaN(Date.parse(updatedAt))
    ) {
      return res.status(400).json({
        code: 400,
        message: 'Invalid updatedAt format',
        data: null
      });
    }

    // 转换字符串为 Date 对象
    if (createdAt && typeof createdAt === 'string') {
      req.body.createdAt = new Date(createdAt);
    }

    if (updatedAt && typeof updatedAt === 'string') {
      req.body.updatedAt = new Date(updatedAt);
    }
  }

  next();
};

/**
 * 记录时间戳操作的中间件（用于调试）
 */
const logTimestamps = (req, res, next) => {
  // 生产环境下可以移除日志输出
  next();
};

module.exports = {
  addCreateTimestamp,
  addUpdateTimestamp,
  autoTimestamp,
  enhancePrismaQuery,
  validateTimestamps,
  logTimestamps
};
