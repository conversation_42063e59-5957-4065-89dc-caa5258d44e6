require('dotenv').config();
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function verifyDatabase() {
  try {
    console.log('🔍 验证数据库清理结果...\n');
    
    // 检查各个表的数据量
    const [
      userCount,
      dishCount,
      menuCount,
      orderCount,
      messageCount,
      notificationCount,
      connectionCount,
      orderPushCount,
      recommendedMenuCount
    ] = await Promise.all([
      prisma.user.count(),
      prisma.dish.count(),
      prisma.menu.count(),
      prisma.order.count(),
      prisma.message.count(),
      prisma.notification.count(),
      prisma.userConnection.count(),
      prisma.orderPush.count(),
      prisma.recommendedMenu.count()
    ]);
    
    console.log('📊 数据库表统计:');
    console.log(`  用户 (User): ${userCount} 条`);
    console.log(`  菜品 (Dish): ${dishCount} 条`);
    console.log(`  菜单 (Menu): ${menuCount} 条`);
    console.log(`  订单 (Order): ${orderCount} 条`);
    console.log(`  留言 (Message): ${messageCount} 条`);
    console.log(`  通知 (Notification): ${notificationCount} 条`);
    console.log(`  用户连接 (UserConnection): ${connectionCount} 条`);
    console.log(`  订单推送 (OrderPush): ${orderPushCount} 条`);
    console.log(`  推荐菜单 (RecommendedMenu): ${recommendedMenuCount} 条`);
    
    // 显示剩余用户详情
    const users = await prisma.user.findMany({
      select: {
        id: true,
        name: true,
        phone: true,
        role: true,
        createdAt: true
      }
    });
    
    console.log('\n👥 剩余用户详情:');
    users.forEach((user, index) => {
      console.log(`  ${index + 1}. ${user.name}`);
      console.log(`     ID: ${user.id}`);
      console.log(`     手机: ${user.phone || '无'}`);
      console.log(`     角色: ${user.role}`);
      console.log(`     创建时间: ${user.createdAt.toLocaleString()}`);
      console.log('');
    });
    
    console.log('✅ 数据库清理验证完成！');
    console.log('✅ 已成功保留张三和李四的账号，清空了其他所有数据。');
    
  } catch (error) {
    console.error('❌ 验证失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

verifyDatabase();
