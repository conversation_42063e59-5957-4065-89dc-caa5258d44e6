/**
 * 统一的时间处理工具
 * 使用dayjs解决小程序中时间显示不一致的问题
 */

// 导入dayjs
const dayjs = require('dayjs');

// 在小程序环境中，我们简化dayjs的使用，不使用插件
// 因为小程序的模块解析机制与Node.js不同

/**
 * 格式化时间显示
 * @param {string|Date|number} dateInput 时间字符串、Date对象或时间戳
 * @param {string} format 格式类型: 'datetime', 'date', 'time', 'relative'
 * @returns {string} 格式化后的时间字符串
 */
function formatTime(dateInput, format = 'datetime') {
  if (!dateInput) return '未设置';

  try {
    console.log('🕒 formatTime 输入:', {
      dateInput,
      type: typeof dateInput,
      format
    });

    // 使用dayjs解析时间
    let dayjsObj;

    if (typeof dateInput === 'string') {
      // 处理各种字符串格式
      dayjsObj = dayjs(dateInput);
    } else if (dateInput instanceof Date) {
      // 处理Date对象
      dayjsObj = dayjs(dateInput);
    } else if (typeof dateInput === 'number') {
      // 处理时间戳
      dayjsObj = dayjs(dateInput);
    } else {
      console.warn('无效的日期输入类型:', typeof dateInput, dateInput);
      return '时间格式错误';
    }

    // 检查日期是否有效
    if (!dayjsObj.isValid()) {
      console.warn('无效的日期:', dateInput);
      return '时间格式错误';
    }

    console.log('✅ dayjs解析成功:', dayjsObj.format());

    // 根据格式类型返回不同的格式
    switch (format) {
      case 'datetime':
        return dayjsObj.format('YYYY-MM-DD HH:mm');
      case 'date':
        return dayjsObj.format('YYYY-MM-DD');
      case 'time':
        return dayjsObj.format('HH:mm');
      case 'relative':
        return formatRelativeTimeInternal(dayjsObj);
      default:
        return dayjsObj.format('YYYY-MM-DD HH:mm');
    }
  } catch (error) {
    console.error('时间格式化失败:', error, dateInput);
    return '时间格式错误';
  }
}

/**
 * 内部相对时间格式化函数
 * @param {dayjs.Dayjs} dayjsObj dayjs对象
 * @returns {string} 相对时间字符串
 */
function formatRelativeTimeInternal(dayjsObj) {
  const now = dayjs();
  const diffMs = now.valueOf() - dayjsObj.valueOf();
  const diffMinutes = Math.floor(diffMs / (1000 * 60));
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

  if (diffMinutes < 1) {
    return '刚刚';
  } else if (diffMinutes < 60) {
    return `${diffMinutes}分钟前`;
  } else if (diffHours < 24) {
    return `${diffHours}小时前`;
  } else if (diffDays < 7) {
    return `${diffDays}天前`;
  } else {
    return dayjsObj.format('YYYY-MM-DD HH:mm');
  }
}

/**
 * 格式化为日期时间 (YYYY-MM-DD HH:mm)
 * @param {string|Date|number} dateInput 时间输入
 * @returns {string} 格式化后的日期时间字符串
 */
function formatDateTime(dateInput) {
  return formatTime(dateInput, 'datetime');
}

/**
 * 格式化为日期 (YYYY-MM-DD)
 * @param {string|Date|number} dateInput 时间输入
 * @returns {string} 格式化后的日期字符串
 */
function formatDate(dateInput) {
  return formatTime(dateInput, 'date');
}

/**
 * 格式化为时间 (HH:mm)
 * @param {string|Date|number} dateInput 时间输入
 * @returns {string} 格式化后的时间字符串
 */
function formatTimeOnly(dateInput) {
  return formatTime(dateInput, 'time');
}

/**
 * 格式化为相对时间 (刚刚、5分钟前、2小时前等)
 * @param {string|Date|number} dateInput 时间输入
 * @returns {string} 格式化后的相对时间字符串
 */
function formatRelativeTime(dateInput) {
  return formatTime(dateInput, 'relative');
}

/**
 * 格式化用餐时间显示 (今天 晚餐、明天 午餐等)
 * @param {string|Date|number} dateInput 时间输入
 * @returns {string} 格式化后的用餐时间字符串
 */
function formatMealTime(dateInput) {
  if (!dateInput) return '未设置';

  try {
    const dayjsObj = dayjs(dateInput);
    if (!dayjsObj.isValid()) {
      return '时间格式错误';
    }

    const now = dayjs();
    const diffDays = dayjsObj.diff(now, 'day');

    let dayText = '';
    if (diffDays === 0) {
      dayText = '今天';
    } else if (diffDays === -1) {
      dayText = '昨天';
    } else if (diffDays === 1) {
      dayText = '明天';
    } else if (diffDays === 2) {
      dayText = '后天';
    } else {
      dayText = dayjsObj.format('MM-DD');
    }

    const hour = dayjsObj.hour();
    let mealTime = '';
    if (hour < 10) {
      mealTime = '早餐';
    } else if (hour < 16) {
      mealTime = '午餐';
    } else {
      mealTime = '晚餐';
    }

    const timeStr = dayjsObj.format('HH:mm');
    return `${dayText} ${mealTime} ${timeStr}`;
  } catch (error) {
    console.error('用餐时间格式化失败:', error);
    return '时间格式错误';
  }
}

/**
 * 判断是否为今天
 * @param {string|Date|number} dateInput 时间输入
 * @returns {boolean} 是否为今天
 */
function isToday(dateInput) {
  if (!dateInput) return false;

  try {
    const dayjsObj = dayjs(dateInput);
    return dayjsObj.isValid() && dayjsObj.isSame(dayjs(), 'day');
  } catch (error) {
    return false;
  }
}

/**
 * 判断是否为昨天
 * @param {string|Date|number} dateInput 时间输入
 * @returns {boolean} 是否为昨天
 */
function isYesterday(dateInput) {
  if (!dateInput) return false;

  try {
    const dayjsObj = dayjs(dateInput);
    return (
      dayjsObj.isValid() && dayjsObj.isSame(dayjs().subtract(1, 'day'), 'day')
    );
  } catch (error) {
    return false;
  }
}

/**
 * 获取友好的日期显示
 * @param {string|Date|number} dateInput 时间输入
 * @returns {string} 友好的日期显示
 */
function getFriendlyDate(dateInput) {
  if (!dateInput) return '未知日期';

  try {
    const dayjsObj = dayjs(dateInput);
    if (!dayjsObj.isValid()) {
      return '日期格式错误';
    }

    if (isToday(dateInput)) {
      return '今天';
    } else if (isYesterday(dateInput)) {
      return '昨天';
    } else {
      const diffDays = dayjs().diff(dayjsObj, 'day');

      if (diffDays < 7 && diffDays > 0) {
        return `${diffDays}天前`;
      } else if (diffDays < 0 && diffDays > -7) {
        return `${Math.abs(diffDays)}天后`;
      } else {
        return dayjsObj.format('MM月DD日');
      }
    }
  } catch (error) {
    console.error('友好日期格式化失败:', error);
    return '日期格式错误';
  }
}

/**
 * 解析用餐时间选择器的值
 * @param {string} selectedTime 选择的时间字符串
 * @returns {string} ISO时间字符串
 */
function parseDiningTimeSelection(selectedTime) {
  if (!selectedTime) {
    return dayjs().toISOString();
  }

  try {
    // 解析选择的时间
    const parts = selectedTime.split(' ');
    const dayOffset = parts[0] === '今天' ? 0 : parts[0] === '明天' ? 1 : 2;
    const timeStr = parts[2] || '18:00';
    const [hour, minute] = timeStr.split(':').map(Number);

    const diningDate = dayjs()
      .add(dayOffset, 'day')
      .hour(hour)
      .minute(minute)
      .second(0)
      .millisecond(0);

    return diningDate.toISOString();
  } catch (error) {
    console.error('解析用餐时间失败:', error);
    return dayjs().toISOString();
  }
}

// 导出所有函数
module.exports = {
  formatTime,
  formatDateTime,
  formatDate,
  formatTimeOnly,
  formatRelativeTime,
  formatMealTime,
  isToday,
  isYesterday,
  getFriendlyDate,
  parseDiningTimeSelection
};
