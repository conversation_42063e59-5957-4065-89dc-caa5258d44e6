/**
 * 菜品工具类
 * 处理菜品下架、删除等相关逻辑
 */

/**
 * 处理菜品显示逻辑
 * @param {Array} dishes 菜品列表
 * @param {Object} options 选项
 * @returns {Array} 处理后的菜品列表
 */
const processDishesForDisplay = (dishes, options = {}) => {
	const {
		showUnpublished = false, // 是否显示下架菜品
			markUnpublished = true, // 是否标记下架菜品
			fallbackImage = ''
	} = options;

	return dishes
		.filter(dish => {
			// 如果不显示下架菜品，过滤掉
			if (!showUnpublished && !dish.isPublished) {
				return false;
			}
			return true;
		})
		.map(dish => {
			const processedDish = {
				...dish
			};

			// 处理下架菜品的显示
			if (!dish.isPublished && markUnpublished) {
				processedDish.name = `${dish.name} (已下架)`;
				processedDish.isUnavailable = true;
				processedDish.unavailableReason = '菜品已下架';
			}

			// 处理缺失的图片
			if (!processedDish.image || processedDish.image === '') {
				processedDish.image = fallbackImage;
			}

			return processedDish;
		});
};

/**
 * 检查菜品是否可用
 * @param {Object} dish 菜品对象
 * @returns {Object} 可用性检查结果
 */
const checkDishAvailability = (dish) => {
	const result = {
		available: true,
		reason: '',
		canOrder: true
	};

	if (!dish) {
		result.available = false;
		result.reason = '菜品不存在';
		result.canOrder = false;
		return result;
	}

	if (!dish.isPublished) {
		result.available = false;
		result.reason = '菜品已下架';
		result.canOrder = false;
	}

	return result;
};

/**
 * 处理菜单中的下架菜品
 * @param {Object} menu 菜单对象
 * @param {Object} options 选项
 * @returns {Object} 处理后的菜单
 */
const processMenuWithUnavailableDishes = (menu, options = {}) => {
	const {
		removeUnavailable = false, // 是否移除不可用菜品
			markUnavailable = true // 是否标记不可用菜品
	} = options;

	if (!menu || !menu.dishes) {
		return menu;
	}

	const processedMenu = {
		...menu
	};

	processedMenu.dishes = menu.dishes
		.map(dish => {
			const availability = checkDishAvailability(dish);
			const processedDish = {
				...dish
			};

			if (!availability.available) {
				if (removeUnavailable) {
					return null; // 将被过滤掉
				}

				if (markUnavailable) {
					processedDish.name = `${dish.name} (${availability.reason})`;
					processedDish.isUnavailable = true;
					processedDish.unavailableReason = availability.reason;
					processedDish.canOrder = availability.canOrder;
				}
			}

			return processedDish;
		})
		.filter(dish => dish !== null); // 移除null项

	// 添加菜单级别的统计信息
	const totalDishes = menu.dishes.length;
	const availableDishes = processedMenu.dishes.filter(dish => !dish.isUnavailable).length;
	const unavailableDishes = totalDishes - availableDishes;

	processedMenu.dishStats = {
		total: totalDishes,
		available: availableDishes,
		unavailable: unavailableDishes,
		hasUnavailable: unavailableDishes > 0
	};

	return processedMenu;
};

/**
 * 处理订单中的下架菜品
 * @param {Object} order 订单对象
 * @returns {Object} 处理后的订单
 */
const processOrderWithUnavailableDishes = (order) => {
	if (!order || !order.items) {
		return order;
	}

	const processedOrder = {
		...order
	};

	// 如果items是JSON字符串，先解析
	let items = order.items;
	if (typeof items === 'string') {
		try {
			items = JSON.parse(items);
		} catch (e) {
			console.error('解析订单items失败:', e);
			return order;
		}
	}

	// 处理每个订单项
	processedOrder.items = items.map(item => {
		const processedItem = {
			...item
		};

		// 如果菜品信息不完整，标记为可能已删除
		if (!item.name || item.name === '') {
			processedItem.name = '未知菜品 (可能已删除)';
			processedItem.isDeleted = true;
		}

		return processedItem;
	});

	return processedOrder;
};

/**
 * 生成菜品影响报告
 * @param {Object} impactData 影响数据
 * @returns {Object} 格式化的报告
 */
const generateDishImpactReport = (impactData) => {
	const report = {
		summary: '',
		details: [],
		recommendations: [],
		severity: 'low' // low, medium, high
	};

	const {
		summary,
		details
	} = impactData;

	// 生成摘要
	if (summary.total === 0) {
		report.summary = '此菜品未被引用，可以安全删除';
		report.severity = 'low';
	} else {
		report.summary = `此菜品被 ${summary.menuItems} 个菜单和 ${summary.orders} 个订单引用`;
		report.severity = summary.total > 10 ? 'high' : summary.total > 3 ? 'medium' : 'low';
	}

	// 生成详细信息
	if (details.menus && details.menus.length > 0) {
		report.details.push({
			type: 'menus',
			title: '相关菜单',
			items: details.menus.map(menu => ({
				id: menu.menuId,
				text: `${menu.creatorName}的菜单 (${new Date(menu.date).toLocaleDateString()})`,
				isOwn: menu.isOwn,
				isToday: menu.isToday
			}))
		});
	}

	if (details.orders && details.orders.length > 0) {
		report.details.push({
			type: 'orders',
			title: '相关订单',
			items: details.orders.map(order => ({
				id: order.orderId,
				text: `${order.userName}的订单 (${new Date(order.date).toLocaleDateString()})`,
				status: order.status
			}))
		});
	}

	// 生成建议
	if (summary.total > 0) {
		if (summary.menuItems > 0) {
			report.recommendations.push('建议先从相关菜单中移除此菜品');
		}
		if (summary.orders > 0) {
			report.recommendations.push('删除后历史订单中的菜品信息将无法正常显示');
		}
		report.recommendations.push('建议先下架菜品，观察一段时间后再考虑删除');
	}

	return report;
};

module.exports = {
	processDishesForDisplay,
	checkDishAvailability,
	processMenuWithUnavailableDishes,
	processOrderWithUnavailableDishes,
	generateDishImpactReport
};