import Toast from '@vant/weapp/lib/toast/toast';
const {orderApi, userApi} = require('../../services/api');
const {retry} = require('../../utils/performance');
const {parseDiningTimeSelection} = require('../../utils/timeUtils');

Page({
  data: {
    basketItems: [],
    showDialog: false,
    showUserSelector: false,
    emptyCartImage: '/assets/image/empty_cart.svg', // 使用 SVG 图片
    defaultFoodImage:
      'https://cdn.pixabay.com/photo/2017/05/07/08/56/food-2290814_1280.jpg',
    orderSubmitted: false,
    remark: '',
    selectedTime: '',
    selectedUser: null,
    familyMembers: [],
    subscriptionAuthStatus: null, // 订阅授权状态
    timeArray: [
      ['今天', '明天', '后天'],
      ['早餐', '午餐', '晚餐'],
      [
        '06:00',
        '07:00',
        '08:00',
        '09:00',
        '10:00',
        '11:00',
        '12:00',
        '13:00',
        '14:00',
        '15:00',
        '16:00',
        '17:00',
        '18:00',
        '19:00',
        '20:00',
        '21:00'
      ]
    ],
    timeIndex: [0, 2, 10] // 默认选择今天晚餐18:00
  },

  onLoad() {
    // 页面加载时的逻辑
    this.initTimeSelector();
    this.loadFamilyMembers();
  },

  onShow() {
    // 从缓存中获取购物篮数据
    this.loadBasketData();

    // 获取订单提交状态
    const orderSubmitted = wx.getStorageSync('orderSubmitted') || false;
    this.setData({orderSubmitted});
  },

  // 加载家庭成员列表
  async loadFamilyMembers() {
    try {
      const result = await userApi.getFamilyMembers();
      if (result.code === 200) {
        const currentUser = wx.getStorageSync('userInfo');
        const allMembers = result.data || [];

        // 从家庭成员列表中排除当前用户自己
        const familyMembers = allMembers.filter(
          member => member.id !== currentUser.id
        );

        // 如果有其他家庭成员，选择第一个；否则显示空列表
        const selectedUser = familyMembers.length > 0 ? familyMembers[0] : null;

        this.setData({
          familyMembers,
          selectedUser
        });

        if (familyMembers.length === 0) {
        }
      }
    } catch (error) {
      console.error('加载家庭成员失败:', error);
      // 如果加载失败，设置空列表
      this.setData({
        familyMembers: [],
        selectedUser: null
      });
    }
  },

  // 初始化时间选择器
  initTimeSelector() {
    const now = new Date();
    const hour = now.getHours();
    let timeIndex = this.data.timeIndex;

    // 根据当前时间设置默认选项
    if (hour < 10) {
      timeIndex = [0, 0, hour - 6 >= 0 ? hour - 6 : 0]; // 早餐
    } else if (hour < 16) {
      timeIndex = [0, 1, hour - 6 >= 0 ? hour - 6 : 0]; // 午餐
    } else {
      timeIndex = [0, 2, hour - 6 >= 0 ? hour - 6 : 0]; // 晚餐
    }

    // 更新默认选择的时间
    this.setData({timeIndex});
    this.updateSelectedTime(timeIndex);
  },

  // 更新选择的时间
  updateSelectedTime(timeIndex) {
    const {timeArray} = this.data;
    const selectedTime = `${timeArray[0][timeIndex[0]]} ${
      timeArray[1][timeIndex[1]]
    } ${timeArray[2][timeIndex[2]]}`;
    this.setData({selectedTime});
  },

  // 时间选择器变化事件
  bindTimeChange(e) {
    const timeIndex = e.detail.value;
    this.setData({timeIndex});
    this.updateSelectedTime(timeIndex);
  },

  // 备注输入事件
  onRemarkInput(e) {
    this.setData({
      remark: e.detail.value
    });
  },

  // 加载购物篮数据
  loadBasketData() {
    const basket = wx.getStorageSync('basket') || {};
    const basketItems = Object.values(basket);

    this.setData({basketItems});
  },

  // 删除菜品
  deleteItem(e) {
    const id = e.currentTarget.dataset.id;
    let basket = wx.getStorageSync('basket') || {};

    if (basket[id]) {
      delete basket[id];
      wx.setStorageSync('basket', basket);
      this.loadBasketData();

      // 提示用户
      Toast.success('已删除');
    }
  },

  // 跳转到点菜页面
  goToOrder() {
    wx.switchTab({
      url: '/pages/order/index'
    });
  },

  // 跳转到历史菜单页面
  goToHistory() {
    wx.navigateTo({
      url: '/pages/history_menu/index'
    });
  },

  // 显示用户选择器
  showUserSelector() {
    this.setData({
      showUserSelector: true
    });
  },

  // 隐藏用户选择器
  hideUserSelector() {
    this.setData({
      showUserSelector: false
    });
  },

  // 跳转到用户关联页面
  goToUserConnection() {
    wx.navigateTo({
      url: '/pages/user_connection/index',
      success: () => {
        // 关闭当前弹窗
        this.hideUserSelector();
      },
      fail: error => {
        console.error('跳转到用户关联页面失败:', error);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  // 选择用户
  selectUser(e) {
    const userId = e.currentTarget.dataset.userId;
    const selectedUser = this.data.familyMembers.find(
      member => member.id === userId
    );

    this.setData({
      selectedUser,
      showUserSelector: false
    });
  },

  // 提交订单（移除防抖，直接执行）
  submitOrder() {
    // 防止重复点击
    if (this.data.orderSubmitted) {
      return;
    }

    if (this.data.basketItems.length === 0) {
      Toast.fail('菜单为空');
      return;
    }

    if (!this.data.selectedUser) {
      Toast.fail('请选择推送人员');
      return;
    }
    // 直接检查订阅授权（必须在用户点击事件中调用）
    this.checkSubscriptionAuthSync();
  },

  // 同步检查订阅授权（在用户点击事件中调用）
  checkSubscriptionAuthSync() {
    try {
      // 检查是否在开发工具中（开发工具没有 wx.requestSubscribeMessage）
      const systemInfo = wx.getSystemInfoSync();
      const isDevTool = systemInfo.platform === 'devtools';

      if (isDevTool) {
        console.log('🔧 开发工具环境，跳过订阅授权');
        this.setData({
          subscriptionAuthStatus: 'devtools'
        });
        this.showConfirmDialog();
        return;
      }

      // 真机环境，每次都请求订阅授权（因为订阅消息是一次性的）
      console.log('📱 真机环境，请求订阅授权');
      wx.requestSubscribeMessage({
        tmplIds: ['kDusapKJllk0UrDuT86oUfFoVID7eHDiQ4AK7i0esNc'],
        success: res => {
          const templateId = 'kDusapKJllk0UrDuT86oUfFoVID7eHDiQ4AK7i0esNc';
          const authResult = res[templateId];

          this.setData({
            subscriptionAuthStatus: authResult
          });

          console.log(`📋 订阅授权结果: ${authResult}`);
          this.showConfirmDialog();
        },
        fail: err => {
          console.error('订阅授权失败:', err);
          this.setData({
            subscriptionAuthStatus: 'failed'
          });
          this.showConfirmDialog();
        }
      });
    } catch (error) {
      console.error('订阅授权检查失败:', error);
      this.setData({
        subscriptionAuthStatus: 'failed'
      });
      this.showConfirmDialog();
    }
  },

  // 显示确认对话框
  showConfirmDialog() {
    wx.showLoading({title: '准备中...'});

    setTimeout(() => {
      wx.hideLoading();
      this.setData({
        showDialog: true,
        orderSubmitted: false // 重置提交状态
      });
    }, 500);
  },

  // 确认提交订单
  async handleConfirm() {
    const {basketItems, remark, selectedTime, selectedUser} = this.data;

    try {
      wx.showLoading({title: '推送中...'});

      // 构建订单数据
      const orderData = {
        menuId: null, // 临时菜单，后端会自动创建
        items: basketItems.map(item => ({
          dishId: item.id,
          name: item.name,
          count: item.count
        })),
        remark: remark || '',
        diningTime: this.formatDiningTime(selectedTime),
        pushToUsers: selectedUser ? [selectedUser.id] : [] // 转换为数组格式
      };

      // 使用重试机制调用API
      const createOrderWithRetry = retry(
        () => orderApi.createOrderAndPush(orderData),
        3, // 最多重试3次
        1000, // 基础延迟1秒
        5000 // 最大延迟5秒
      );

      const result = await createOrderWithRetry();

      if (result.code === 200 || result.code === 201) {
        const {order, menu, pushStatus} = result.data;

        // 检查数据完整性
        if (order?.menuId && !menu) {
          console.warn('⚠️  订单有菜单ID但菜单数据为空');
        }

        // 验证返回的数据
        if (!order) {
          console.error('❌ 订单数据为空');
          Toast.fail('订单创建失败，数据异常');
          return;
        }

        // 构建历史菜单数据（本地缓存）
        const todayMenu = {
          id: menu?.id || order.menuId || `temp_${Date.now()}`, // 使用菜单ID，如果没有则使用订单中的menuId，最后使用临时ID
          orderId: order.id,
          date: selectedTime || '今日',
          dishes: basketItems,
          remark: remark,
          createdAt: new Date().toISOString(),
          status: 'pending',
          pushedTo: selectedUser.name,
          pushStatus: pushStatus
        };

        // 更新本地历史菜单缓存
        await this.updateLocalHistory(todayMenu);

        // 清空购物篮
        wx.setStorageSync('basket', {});

        // 设置订单已提交状态
        wx.setStorageSync('orderSubmitted', true);

        // 关闭对话框
        this.setData({
          showDialog: false,
          basketItems: [],
          orderSubmitted: true
        });

        // 注意：微信订阅消息已在后端 orderPushController.createOrderAndPush 中发送
        // 这里不需要重复发送，避免用户收到重复的微信通知

        // 显示推送成功提示
        Toast.success({
          message: '菜单推送成功',
          forbidClick: true,
          duration: 1500
        });

        // 延迟跳转到首页或历史菜单页面
        setTimeout(() => {
          wx.switchTab({
            url: '/pages/home/<USER>'
          });
        }, 1500);
      } else {
        throw new Error(result.message || '提交失败');
      }
    } catch (error) {
      console.error('提交订单失败:', error);
      Toast.fail(error.message || '提交失败，请重试');
    } finally {
      wx.hideLoading();
    }
  },

  // 发送推送通知
  async sendPushNotification(user, menu) {
    try {
      const {notificationApi, connectionApi} = require('../../services/api');

      // 首先检查是否与目标用户有关联关系
      const connections = await connectionApi.getMyConnections('accepted');
      const isConnected =
        connections.data &&
        connections.data.some(
          conn => conn.sender.id === user.id || conn.receiver.id === user.id
        );

      if (!isConnected) {
        console.warn('目标用户不在关联列表中，无法推送通知');
        Toast.fail('只能向关联用户推送通知');
        return;
      }

      // 构建推送消息
      const message = {
        title: '今日菜单推送',
        content: `${user.name}，今日菜单已为您准备好：${menu.dishes
          .map(d => d.name)
          .join('、')}`,
        type: 'menu_push',
        targetUserId: user.id,
        data: {
          menuId: menu.id,
          dishes: menu.dishes,
          diningTime: menu.date,
          remark: menu.remark
        }
      };

      // 发送推送通知
      const result = await notificationApi.createNotification(message);

      if (result.code === 200) {
        console.log('推送通知发送成功');
      } else {
        console.warn('推送通知发送失败:', result.message);
      }
    } catch (error) {
      console.error('发送推送通知失败:', error);
      // 推送失败不影响主流程
    }
  },

  // 格式化用餐时间
  formatDiningTime(selectedTime) {
    return parseDiningTimeSelection(selectedTime);
  },

  // 查看今日菜单
  viewTodayMenu() {
    wx.navigateTo({
      url: '/pages/history_menu/index?viewToday=true'
    });
  },

  // 继续点菜
  continueOrder() {
    // 重置订单已提交状态
    wx.setStorageSync('orderSubmitted', false);

    this.setData({
      orderSubmitted: false
    });
    if (this.data.basketItems.length > 0) {
      return;
    }
    // 跳转到点菜页面
    wx.switchTab({
      url: '/pages/order/index'
    });
  },

  // 更新本地历史菜单缓存
  async updateLocalHistory(todayMenu) {
    try {
      // 获取历史菜单列表
      let historyMenus = wx.getStorageSync('historyMenus') || [];
      historyMenus.unshift(todayMenu);

      // 最多保存20条历史记录
      if (historyMenus.length > 20) {
        historyMenus = historyMenus.slice(0, 20);
      }

      // 保存历史菜单和今日菜单
      wx.setStorageSync('historyMenus', historyMenus);
      wx.setStorageSync('todayMenu', todayMenu);
    } catch (error) {
      console.error('更新本地历史失败:', error);
    }
  },

  // 取消提交订单
  handleCancel() {
    this.setData({
      showDialog: false,
      orderSubmitted: false // 重置提交状态
    });
  }
});
