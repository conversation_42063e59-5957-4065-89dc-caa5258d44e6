/* 通用缺省页组件样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  text-align: center;

  .empty-icon {
    margin-bottom: 24rpx;
    opacity: 0.6;
    transition: opacity 0.3s ease;
  }

  .empty-text {
    font-size: 28rpx;
    color: #6b7280;
    font-weight: 500;
    margin-bottom: 12rpx;
    line-height: 1.4;
  }

  .empty-desc {
    font-size: 24rpx;
    color: #9ca3af;
    line-height: 1.5;
    margin-bottom: 40rpx;
    max-width: 400rpx;
  }

  .empty-action {
    /* 按钮样式由van-button组件控制 */
  }

  /* 主题变体 */
  &.theme-light {
    background: #fafafa;
    border-radius: 16rpx;
    border: 2rpx dashed #e5e7eb;
  }

  &.theme-card {
    background: #ffffff;
    border-radius: 16rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  }

  &.theme-minimal {
    padding: 40rpx 20rpx;
    
    .empty-text {
      font-size: 26rpx;
    }
    
    .empty-desc {
      font-size: 22rpx;
      margin-bottom: 30rpx;
    }
  }

  /* 尺寸变体 */
  &.size-small {
    padding: 40rpx 20rpx;
    
    .empty-icon {
      margin-bottom: 16rpx;
    }
    
    .empty-text {
      font-size: 26rpx;
      margin-bottom: 8rpx;
    }
    
    .empty-desc {
      font-size: 22rpx;
      margin-bottom: 24rpx;
    }
  }

  &.size-large {
    padding: 120rpx 60rpx;
    
    .empty-icon {
      margin-bottom: 32rpx;
    }
    
    .empty-text {
      font-size: 32rpx;
      margin-bottom: 16rpx;
    }
    
    .empty-desc {
      font-size: 26rpx;
      margin-bottom: 48rpx;
    }
  }

  /* 动画效果 */
  &.animated {
    animation: fadeInUp 0.6s ease-out;
  }
}

/* 淡入向上动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
