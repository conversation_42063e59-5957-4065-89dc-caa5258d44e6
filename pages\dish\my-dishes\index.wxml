<view class="container">
  <!-- 页面标题和统计 -->
  <view class="header-section">
    <view class="title-row">
      <view class="page-title">
        <van-icon name="apps-o" size="40rpx" color="#3B82F6" />
        <text class="title-text">我的菜品</text>
      </view>
    </view>

    <!-- 筛选标签 -->
    <view class="filter-tabs">
      <view
        class="filter-tab {{currentFilter === 'all' ? 'active' : ''}}"
        bindtap="switchFilter"
        data-filter="all"
      >
        全部 ({{publishedCount + unpublishedCount}})
      </view>
      <view
        class="filter-tab {{currentFilter === 'published' ? 'active' : ''}}"
        bindtap="switchFilter"
        data-filter="published"
      >
        已上架 ({{publishedCount}})
      </view>
      <view
        class="filter-tab {{currentFilter === 'unpublished' ? 'active' : ''}}"
        bindtap="switchFilter"
        data-filter="unpublished"
      >
        未上架 ({{unpublishedCount}})
      </view>
    </view>
  </view>

  <!-- 菜品列表 -->
  <refresh-scroll
    id="refresh-scroll"
    container-height="{{scrollViewHeight}}"
    request-url="{{requestUrl}}"
    request-params="{{requestParams}}"
    request-headers="{{requestHeaders}}"
    page-size="10"
    data-field="data"
    list-field="list"
    total-field="total"
    auto-load="{{true}}"
    is-empty="{{dishes.length === 0}}"
    empty-icon="apps-o"
    empty-text="{{emptyTitle}}"
    empty-tip="{{emptyDesc}}"
    bind:datachange="onDataChange"
  >
    <!-- 外部渲染菜品列表 -->
    <view slot="content" wx:if="{{dishes.length > 0}}" class="dishes-list">
      <view
        wx:for="{{dishes}}"
        wx:key="id"
        class="dish-card"
        bindtap="viewDishDetail"
        data-dish="{{item}}"
      >
        <!-- 卡片头部：左边图片，右边文字 -->
        <view class="dish-header-layout">
          <!-- 左侧图片 -->
          <view class="dish-image-container">
            <safe-image
              src="{{item.image}}"
              imageType="dish"
              custom-class="dish-image-container"
              image-class="dish-image"
              mode="aspectFill"
              lazyLoad="{{true}}"
              previewable="{{true}}"
            />
            <!-- 状态标签 -->
            <view
              class="status-badge {{item.isPublished ? 'published' : 'unpublished'}}"
            >
              {{item.isPublished ? '已上架' : '未上架'}}
            </view>
          </view>

          <!-- 右侧信息 -->
          <view class="dish-info">
            <view class="dish-title-row">
              <text class="dish-name">{{item.name}}</text>
              <text class="dish-category">{{item.category.name}}</text>
            </view>

            <view class="dish-description">
              <text class="ingredients-preview">{{item.ingredients}}</text>
            </view>

            <view class="dish-meta">
              <view class="meta-item">
                <van-icon name="clock-o" size="24rpx" color="#9CA3AF" />
                <text class="meta-text">{{item.formattedTime}}</text>
              </view>
              <view
                wx:if="{{item._count && item._count.menuItems > 0}}"
                class="meta-item"
              >
                <van-icon name="star-o" size="24rpx" color="#9CA3AF" />
                <text class="meta-text"
                  >被使用 {{item._count.menuItems}} 次</text
                >
              </view>
            </view>
          </view>
        </view>

        <!-- 底部标签 -->
        <view
          wx:if="{{item.formattedTags && item.formattedTags.length > 0}}"
          class="dish-tags"
        >
          <view
            wx:for="{{item.formattedTags}}"
            wx:for-item="tagLabel"
            wx:key="*this"
            class="tag-item"
          >
            {{tagLabel}}
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="dish-actions">
          <!-- 上架/下架按钮 -->
          <view
            class="action-btn status-btn {{item.isPublished ? 'unpublish' : 'publish'}}"
            catchtap="togglePublishStatus"
            data-id="{{item.id}}"
            data-status="{{item.isPublished}}"
          >
            <!-- 眼睛图标方案 -->
            <van-icon
              name="{{item.isPublished ? 'closed-eye' : 'eye-o'}}"
              size="32rpx"
              color="{{item.isPublished ? '#ea580c' : '#059669'}}"
            />
            <text
              class="action-text"
              >{{item.isPublished ? '下架' : '上架'}}</text
            >
          </view>

          <!-- 编辑按钮 -->
          <view
            class="action-btn edit-btn"
            catchtap="editDish"
            data-id="{{item.id}}"
          >
            <van-icon name="edit" size="32rpx" />
            <text class="action-text">编辑</text>
          </view>

          <!-- 删除按钮 -->
          <view
            class="action-btn delete-btn"
            catchtap="deleteDish"
            data-id="{{item.id}}"
            data-name="{{item.name}}"
          >
            <van-icon name="delete-o" size="32rpx" />
            <text class="action-text">删除</text>
          </view>
        </view>
      </view>
    </view>
  </refresh-scroll>

  <!-- 悬浮添加按钮 -->
  <view class="fab-container">
    <view class="fab-btn" bindtap="goToAddDish">
      <van-icon name="plus" size="48rpx" color="#FFFFFF" />
    </view>
  </view>
</view>

<!-- 删除确认弹窗 -->
<van-dialog
  id="van-dialog"
  confirm-button-color="#EF4444"
  cancel-button-color="#666666"
  width="600rpx"
/>

<!-- 加载提示 -->
<van-toast id="van-toast" />
