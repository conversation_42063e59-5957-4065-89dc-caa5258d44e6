const express = require('express');
const router = express.Router();
const dishController = require('../controllers/dishController');
const {auth, adminAuth} = require('../middlewares/auth');
const {
  autoTimestamp,
  enhancePrismaQuery,
  validateTimestamps,
  logTimestamps
} = require('../middlewares/timestampMiddleware');

// 获取菜品列表
router.get('/', dishController.getDishes);

// 获取菜品分类
router.get('/categories', dishController.getCategories);

// 获取分类列表（小程序专用）
router.get(
  '/categories/miniprogram',
  dishController.getCategoriesForMiniProgram
);

// 获取我的菜品列表
router.get('/my', auth, dishController.getMyDishes);

// 获取菜品统计信息
router.get('/statistics', dishController.getDishStatistics);

// 获取热门菜品
router.get('/hot', dishController.getHotDishes);

// 获取分类菜品（小程序专用，需要认证以支持用户关联过滤）
router.get('/by-category', auth, dishController.getDishesByCategory);

// 获取菜品详情（小程序专用）
router.get('/:id/detail', dishController.getDishDetail);

// 获取菜品影响分析 (需要认证)
router.get('/:id/impact', auth, dishController.getDishImpact);

// 获取指定菜品
router.get('/:id', dishController.getDishById);

// 创建菜品 (普通用户也可以创建)
router.post(
  '/',
  auth,
  validateTimestamps,
  autoTimestamp,
  enhancePrismaQuery,
  logTimestamps,
  dishController.createDish
);

// 更新菜品上架状态 (用户可以更新自己的菜品)
router.put(
  '/:id/status',
  auth,
  validateTimestamps,
  autoTimestamp,
  enhancePrismaQuery,
  logTimestamps,
  dishController.updateDishStatus
);

// 更新菜品 (用户可以更新自己的菜品)
router.put(
  '/:id',
  auth,
  validateTimestamps,
  autoTimestamp,
  enhancePrismaQuery,
  logTimestamps,
  dishController.updateDish
);

// 删除菜品 (用户可以删除自己的菜品)
router.delete('/:id', auth, dishController.deleteDish);

// 创建菜品分类 (需要管理员权限)
router.post(
  '/categories',
  auth,
  adminAuth,
  validateTimestamps,
  autoTimestamp,
  enhancePrismaQuery,
  logTimestamps,
  dishController.createCategory
);

// 更新菜品分类 (需要管理员权限)
router.put(
  '/categories/:id',
  auth,
  adminAuth,
  validateTimestamps,
  autoTimestamp,
  enhancePrismaQuery,
  logTimestamps,
  dishController.updateCategory
);

// 删除菜品分类 (需要管理员权限)
router.delete(
  '/categories/:id',
  auth,
  adminAuth,
  dishController.deleteCategory
);

// 批量操作菜品 (需要管理员权限)
router.post('/batch', auth, adminAuth, dishController.batchOperation);

module.exports = router;
