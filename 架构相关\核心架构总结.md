# 🏗️ 楠楠家厨 - 核心架构总结

## 📋 项目概述
**项目名称**: 楠楠家厨 (wx-nan)  
**项目类型**: 微信小程序 + 后端管理系统  
**主要功能**: 家庭菜单管理和订餐系统  

## 🏛️ 整体架构
```
微信小程序 ←→ 后端API服务 ←→ PostgreSQL数据库
管理后台   ←→ (Express.js) ←→ (Neon云数据库)
```

## 📱 前端架构

### 1. 微信小程序 (根目录)
- **框架**: 微信小程序原生开发
- **UI组件**: Vant Weapp
- **状态管理**: MobX
- **样式**: SCSS + Tailwind CSS

**核心页面**:
- `pages/home/<USER>
- `pages/order/` - 点菜页面  
- `pages/add_menu/` - 新增菜单
- `pages/mine/` - 个人中心
- `pages/message/` - 消息页面

### 2. 管理后台 (webs/admin)
- **框架**: Vue 3 + Vite
- **UI组件**: Element Plus
- **状态管理**: Pinia
- **图表**: ECharts

## 🔧 后端架构 (webs/server)

### 技术栈
- **运行时**: Node.js
- **框架**: Express.js
- **数据库**: PostgreSQL (Neon云数据库)
- **ORM**: Prisma
- **认证**: JWT

### 核心控制器
```
src/controllers/
├── authController.js      # 认证控制器
├── userController.js      # 用户控制器
├── menuController.js      # 菜单控制器
├── orderController.js     # 订单控制器
├── messageController.js   # 消息控制器
└── connectionController.js # 用户关联控制器
```

### API端点
```
/api/auth/*        # 认证相关 (登录/注册)
/api/users/*       # 用户管理
/api/menus/*       # 菜单管理
/api/dishes/*      # 菜品管理
/api/orders/*      # 订单管理
/api/messages/*    # 消息管理
/api/connections/* # 用户关联
```

## 🗄️ 数据库架构

### 核心数据模型
```prisma
model User {
  id       String @id @default(cuid())
  name     String
  phone    String? @unique
  openid   String? @unique
  role     String @default("user")
  # 关联: orders, messages, notifications, connections
}

model Dish {
  id          String @id @default(cuid())
  name        String
  description String?
  image       String
  categoryId  String
  createdBy   String
  # 关联: category, creator, menuItems
}

model Menu {
  id        String @id @default(cuid())
  createdBy String
  date      DateTime
  isToday   Boolean @default(false)
  # 关联: creator, items, orders
}

model Order {
  id         String @id @default(cuid())
  userId     String
  menuId     String
  items      Json
  status     String @default("pending")
  # 关联: user, menu
}

model Message {
  id      String @id @default(cuid())
  content String
  userId  String
  read    Boolean @default(false)
  # 关联: user, recipients
}

model UserConnection {
  id         String @id @default(cuid())
  senderId   String
  receiverId String
  status     String @default("pending")
  # 关联: sender, receiver
}
```

## 🚀 部署架构

### 环境配置
- **开发环境**: 本地SQLite数据库
- **生产环境**: Neon PostgreSQL云数据库
- **图床服务**: PicX (GitHub)
- **小程序**: 微信开发者工具发布

### 环境变量 (.env)
```bash
# 数据库
DATABASE_URL="file:./dev.db"  # 开发环境
# DATABASE_URL="postgresql://..." # 生产环境

# JWT配置
JWT_SECRET="your-jwt-secret"
JWT_EXPIRES_IN="7d"

# 微信配置
WECHAT_APPID="wx82283b353918af82"
WECHAT_SECRET="..."

# PicX图床配置
PICX_TOKEN="ghp_..."
PICX_REPO="2497462726/picx-images-hosting"
```

## 🛠️ 开发指南

### 快速启动
```bash
# 后端服务
cd webs/server
npm install
npm run generate    # 生成Prisma客户端
npm run db:push     # 同步数据库
npm run dev         # 启动服务器

# 管理后台
cd webs/admin
npm install
npm run dev

# 小程序
# 使用微信开发者工具打开项目根目录
```

### 数据库管理
```bash
npm run studio      # 启动Prisma Studio (http://localhost:5555)
npm run db:export   # 导出数据
npm run db:import   # 导入数据
npm run db:clear    # 清空数据
```

## 🌟 核心功能特色

### 1. 家庭菜单管理
- 菜单创建和分享
- 菜品分类管理
- 历史菜单查看

### 2. 用户关联系统
- 家庭成员关联
- 关联申请流程
- 分组管理

### 3. 订单系统
- 快速下单
- 订单统计
- 状态跟踪

### 4. 消息通知
- 实时消息推送
- 微信订阅消息
- 消息分类管理

### 5. 权限管理
- 多角色支持 (admin/user)
- 功能权限控制
- 数据权限隔离

## 📊 项目结构
```
wx-nan/
├── pages/              # 小程序页面
├── components/         # 小程序组件
├── services/          # API服务
├── webs/
│   ├── server/        # 后端服务
│   │   ├── src/       # 源代码
│   │   ├── prisma/    # 数据库模式
│   │   └── scripts/   # 工具脚本
│   └── admin/         # 管理后台
│       └── src/       # Vue源代码
└── 架构相关/           # 架构文档
```

## 🔧 关键配置文件
- `webs/server/.env` - 环境变量配置
- `webs/server/prisma/schema.prisma` - 数据库模式
- `app.json` - 小程序配置
- `webs/admin/vite.config.js` - Vue构建配置
