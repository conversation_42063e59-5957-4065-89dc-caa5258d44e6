// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id            String         @id @default(cuid())
  name          String
  phone         String?        @unique
  email         String?        @unique
  password      String?
  avatar        String?
  openid        String?        @unique
  role          String         @default("user") // user, admin
  status        String         @default("active") // active, inactive, suspended
  lastLoginAt   DateTime?
  loginFailCount Int           @default(0)

  // 用户详细资料
  gender        String?        // male, female, other
  birthday      DateTime?
  address       String?
  bio           String?        // 个人简介
  preferences   Json?          // 用户偏好设置 JSON

  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt
  orders        Order[]
  messages      Message[]
  notifications Notification[]
  sentNotifications Notification[] @relation("NotificationSender")
  messageRecipients MessageRecipient[] @relation("MessageRecipients")

  // 用户关联关系 - 发起的关联
  sentConnections UserConnection[] @relation("ConnectionSender")
  // 用户关联关系 - 接收的关联
  receivedConnections UserConnection[] @relation("ConnectionReceiver")


  // 创建的菜单
  createdMenus Menu[]

  // 创建的菜品
  createdDishes Dish[]

  // 订单推送关系
  sentOrderPushes OrderPush[] @relation("OrderPushSender")
  receivedOrderPushes OrderPush[] @relation("OrderPushReceiver")

  // 推荐菜单关系
  recommendedMenus RecommendedMenu[] // 用户收到的推荐菜单
  sourceRecommendedMenus RecommendedMenu[] @relation("RecommendedMenuSource") // 用户创建的菜单被推荐给其他人

  @@index([phone])
  @@index([email])
  @@index([role])
  @@index([status])
}

model Dish {
  id            String     @id @default(cuid())
  name          String     // 菜品名称
  description   String?    // 菜品描述
  ingredients   String     // 原材料 (改为必填)
  cookingMethod String     // 制作方法 (改为必填)
  remark        String?    // 备注说明
  image         String     // 菜品图片 (改为必填)
  tags          String?    // 标签JSON字符串 (新增)
  isPublished   Boolean    @default(false) // 是否上架 (新增)
  createdBy     String     // 创建者ID (新增)
  categoryId    String     // 分类ID
  category      Category   @relation(fields: [categoryId], references: [id])
  creator       User       @relation(fields: [createdBy], references: [id]) // 新增关联
  createdAt     DateTime   @default(now())
  updatedAt     DateTime   @updatedAt
  menuItems     MenuItem[]

  @@index([categoryId])
  @@index([createdBy])    // 新增索引
  @@index([isPublished]) // 新增索引
}

model Category {
  id        String   @id @default(cuid())
  name      String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  dishes    Dish[]
}

model Menu {
  id        String     @id @default(cuid())
  createdBy String     // 菜单创建者ID
  date      DateTime
  isToday   Boolean    @default(false)
  remark    String?
  deleted   Boolean    @default(false)
  deletedAt DateTime?
  createdAt DateTime   @default(now())
  updatedAt DateTime   @updatedAt

  // 关联关系
  creator   User       @relation(fields: [createdBy], references: [id])
  items     MenuItem[]
  orders    Order[]
  recommendedMenus RecommendedMenu[] // 基于此菜单生成的推荐菜单

  @@index([createdBy])
  @@index([deleted])
  @@index([isToday])
  @@index([createdBy, deleted])
  @@index([isToday, deleted])
  @@index([createdAt, deleted])
}

// 推荐菜单表
model RecommendedMenu {
  id           String   @id @default(cuid())
  userId       String   // 推荐给哪个用户
  sourceMenuId String   // 来源菜单ID
  sourceUserId String   // 来源用户ID（创建菜单的用户）
  priority     Int      @default(0) // 推荐优先级，数字越大优先级越高
  reason       String?  // 推荐理由
  isRead       Boolean  @default(false) // 是否已读
  isActive     Boolean  @default(true) // 是否有效
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // 关联关系
  user       User @relation(fields: [userId], references: [id], onDelete: Cascade)
  sourceMenu Menu @relation(fields: [sourceMenuId], references: [id], onDelete: Cascade)
  sourceUser User @relation("RecommendedMenuSource", fields: [sourceUserId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([sourceMenuId])
  @@index([sourceUserId])
  @@index([userId, isActive])
  @@index([userId, createdAt])
  @@index([priority, createdAt])
}

model MenuItem {
  id        String   @id @default(cuid())
  menuId    String
  dishId    String
  count     Int
  menu      Menu     @relation(fields: [menuId], references: [id], onDelete: Cascade)
  dish      Dish     @relation(fields: [dishId], references: [id])
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([menuId])
  @@index([dishId])
}

model Order {
  id         String    @id @default(cuid())
  userId     String
  menuId     String    // 关联的菜单ID
  user       User      @relation(fields: [userId], references: [id])
  menu       Menu      @relation(fields: [menuId], references: [id])
  items      Json      // 存储订单项的 JSON 数据
  remark     String?
  diningTime DateTime?
  status     String    @default("pending")
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt

  // 订单推送关系
  orderPushes OrderPush[]

  @@index([userId])
  @@index([menuId])
  @@index([status])
  @@index([userId, status])
  @@index([createdAt, userId])
  @@index([diningTime, userId])
}

model Message {
  id        String   @id @default(cuid())
  content   String
  userId    String
  user      User     @relation(fields: [userId], references: [id])
  read      Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 留言接收者关系
  recipients MessageRecipient[]

  @@index([userId])
  @@index([createdAt])
}

model MessageRecipient {
  id        String   @id @default(cuid())
  messageId String
  userId    String   // 接收者ID
  read      Boolean  @default(false)
  readAt    DateTime?
  createdAt DateTime @default(now())

  // 关联关系
  message   Message  @relation(fields: [messageId], references: [id], onDelete: Cascade)
  user      User     @relation("MessageRecipients", fields: [userId], references: [id], onDelete: Cascade)

  @@unique([messageId, userId])
  @@index([userId])
  @@index([messageId])
}

model Notification {
  id        String   @id @default(cuid())
  title     String   @default("新通知")
  content   String
  type      String   @default("general") // 通知类型：general, family, admin, new_dish, new_order, menu_push, etc.
  userId    String
  user      User     @relation(fields: [userId], references: [id])
  senderId  String?
  sender    User?    @relation("NotificationSender", fields: [senderId], references: [id])
  data      String?  // JSON 格式的额外数据
  read      Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([userId])
  @@index([senderId])
  @@index([type])
  @@index([read])
}

model UserConnection {
  id          String   @id @default(cuid())
  senderId    String   // 发起关联的用户ID
  receiverId  String   // 接收关联的用户ID
  status      String   @default("pending") // pending, accepted, rejected
  message     String?  // 申请消息
  remark      String?  // 关联备注

  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 关联关系
  sender      User     @relation("ConnectionSender", fields: [senderId], references: [id], onDelete: Cascade)
  receiver    User     @relation("ConnectionReceiver", fields: [receiverId], references: [id], onDelete: Cascade)

  @@unique([senderId, receiverId]) // 防止重复关联
  @@index([senderId])
  @@index([receiverId])
  @@index([status])
}



// 订单推送模型
model OrderPush {
  id           String   @id @default(cuid())
  orderId      String   // 订单ID
  pushedBy     String   // 推送者ID
  targetUserId String   // 目标用户ID
  message      String?  // 推送消息
  createdAt    DateTime @default(now())

  // 关联关系
  order        Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)
  pusher       User     @relation("OrderPushSender", fields: [pushedBy], references: [id], onDelete: Cascade)
  targetUser   User     @relation("OrderPushReceiver", fields: [targetUserId], references: [id], onDelete: Cascade)

  @@index([orderId])
  @@index([pushedBy])
  @@index([targetUserId])
}


