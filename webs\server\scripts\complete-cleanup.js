require('dotenv').config();
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkAllTables() {
  try {
    console.log('🔍 检查所有表的数据情况...\n');
    
    // 检查所有表的数据量
    const [
      userCount,
      dishCount,
      menuCount,
      menuItemCount,
      orderCount,
      messageCount,
      messageRecipientCount,
      notificationCount,
      connectionCount,
      orderPushCount,
      recommendedMenuCount,
      categoryCount
    ] = await Promise.all([
      prisma.user.count(),
      prisma.dish.count(),
      prisma.menu.count(),
      prisma.menuItem.count(),
      prisma.order.count(),
      prisma.message.count(),
      prisma.messageRecipient.count(),
      prisma.notification.count(),
      prisma.userConnection.count(),
      prisma.orderPush.count(),
      prisma.recommendedMenu.count(),
      prisma.category.count()
    ]);
    
    console.log('📊 所有表的数据统计:');
    console.log(`  用户 (User): ${userCount} 条`);
    console.log(`  菜品 (Dish): ${dishCount} 条`);
    console.log(`  菜单 (Menu): ${menuCount} 条`);
    console.log(`  菜单项 (MenuItem): ${menuItemCount} 条`);
    console.log(`  订单 (Order): ${orderCount} 条`);
    console.log(`  留言 (Message): ${messageCount} 条`);
    console.log(`  留言接收者 (MessageRecipient): ${messageRecipientCount} 条`);
    console.log(`  通知 (Notification): ${notificationCount} 条`);
    console.log(`  用户连接 (UserConnection): ${connectionCount} 条`);
    console.log(`  订单推送 (OrderPush): ${orderPushCount} 条`);
    console.log(`  推荐菜单 (RecommendedMenu): ${recommendedMenuCount} 条`);
    console.log(`  分类 (Category): ${categoryCount} 条`);
    
    return {
      userCount, dishCount, menuCount, menuItemCount, orderCount,
      messageCount, messageRecipientCount, notificationCount,
      connectionCount, orderPushCount, recommendedMenuCount, categoryCount
    };
    
  } catch (error) {
    console.error('❌ 检查失败:', error);
    throw error;
  }
}

async function completeCleanup() {
  try {
    // 1. 先检查张三和李四的ID
    console.log('🔍 查找张三和李四...');
    const zhangsan = await prisma.user.findFirst({ where: { name: '张三' } });
    const lisi = await prisma.user.findFirst({ where: { name: '李四' } });
    
    if (!zhangsan || !lisi) {
      console.log('❌ 未找到张三或李四');
      return;
    }
    
    const keepUserIds = [zhangsan.id, lisi.id];
    console.log('保留用户ID:', keepUserIds);
    
    // 2. 彻底清理所有数据（除了张三和李四）
    console.log('\n🧹 开始彻底清理...');
    
    // 删除推荐菜单
    console.log('1️⃣ 清理推荐菜单...');
    await prisma.recommendedMenu.deleteMany({});
    
    // 删除订单推送
    console.log('2️⃣ 清理订单推送...');
    await prisma.orderPush.deleteMany({});
    
    // 删除留言接收者
    console.log('3️⃣ 清理留言接收者...');
    await prisma.messageRecipient.deleteMany({});
    
    // 删除用户连接
    console.log('4️⃣ 清理用户连接...');
    await prisma.userConnection.deleteMany({});
    
    // 删除通知（除了张三和李四的）
    console.log('5️⃣ 清理通知...');
    await prisma.notification.deleteMany({
      where: {
        AND: [
          { userId: { notIn: keepUserIds } },
          { OR: [{ senderId: null }, { senderId: { notIn: keepUserIds } }] }
        ]
      }
    });
    
    // 删除留言（除了张三和李四的）
    console.log('6️⃣ 清理留言...');
    await prisma.message.deleteMany({
      where: { userId: { notIn: keepUserIds } }
    });
    
    // 删除订单（除了张三和李四的）
    console.log('7️⃣ 清理订单...');
    await prisma.order.deleteMany({
      where: { userId: { notIn: keepUserIds } }
    });
    
    // 删除菜单项（先找到要删除的菜单）
    console.log('8️⃣ 清理菜单项...');
    const menusToDelete = await prisma.menu.findMany({
      where: { createdBy: { notIn: keepUserIds } },
      select: { id: true }
    });
    
    if (menusToDelete.length > 0) {
      const menuIds = menusToDelete.map(m => m.id);
      await prisma.menuItem.deleteMany({
        where: { menuId: { in: menuIds } }
      });
    }
    
    // 删除菜单（除了张三和李四创建的）
    console.log('9️⃣ 清理菜单...');
    await prisma.menu.deleteMany({
      where: { createdBy: { notIn: keepUserIds } }
    });
    
    // 删除菜品（除了张三和李四创建的）
    console.log('🔟 清理菜品...');
    await prisma.dish.deleteMany({
      where: { createdBy: { notIn: keepUserIds } }
    });
    
    // 删除其他用户
    console.log('1️⃣1️⃣ 清理其他用户...');
    await prisma.user.deleteMany({
      where: { id: { notIn: keepUserIds } }
    });
    
    // 清理所有张三和李四的关联数据（如果需要完全清空）
    console.log('1️⃣2️⃣ 清理张三和李四的所有数据（除了账号）...');
    
    // 清理他们的通知
    await prisma.notification.deleteMany({
      where: {
        OR: [
          { userId: { in: keepUserIds } },
          { senderId: { in: keepUserIds } }
        ]
      }
    });
    
    // 清理他们的留言
    await prisma.message.deleteMany({
      where: { userId: { in: keepUserIds } }
    });
    
    // 清理他们的订单
    await prisma.order.deleteMany({
      where: { userId: { in: keepUserIds } }
    });
    
    // 清理他们的菜单项
    const theirMenus = await prisma.menu.findMany({
      where: { createdBy: { in: keepUserIds } },
      select: { id: true }
    });
    
    if (theirMenus.length > 0) {
      const theirMenuIds = theirMenus.map(m => m.id);
      await prisma.menuItem.deleteMany({
        where: { menuId: { in: theirMenuIds } }
      });
    }
    
    // 清理他们的菜单
    await prisma.menu.deleteMany({
      where: { createdBy: { in: keepUserIds } }
    });
    
    // 清理他们的菜品
    await prisma.dish.deleteMany({
      where: { createdBy: { in: keepUserIds } }
    });
    
    console.log('\n✅ 彻底清理完成！');
    
  } catch (error) {
    console.error('❌ 清理失败:', error);
    throw error;
  }
}

async function main() {
  try {
    // 1. 检查当前状态
    console.log('=== 清理前状态 ===');
    await checkAllTables();
    
    // 2. 执行彻底清理
    await completeCleanup();
    
    // 3. 检查清理后状态
    console.log('\n=== 清理后状态 ===');
    const finalCounts = await checkAllTables();
    
    // 4. 验证结果
    console.log('\n🔍 最终验证...');
    const users = await prisma.user.findMany({
      select: { id: true, name: true, phone: true }
    });
    
    console.log('剩余用户:');
    users.forEach(user => {
      console.log(`  ✅ ${user.name} (${user.phone})`);
    });
    
    // 检查是否还有其他数据
    const hasOtherData = Object.values(finalCounts).some((count, index) => {
      // 用户表应该有2条记录（张三和李四），其他表应该都是0
      if (index === 0) return count !== 2; // userCount
      return count > 0;
    });
    
    if (hasOtherData) {
      console.log('⚠️  还有一些数据未清理完全');
    } else {
      console.log('✅ 数据库已完全清理，只保留张三和李四的账号');
    }
    
  } catch (error) {
    console.error('❌ 操作失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
