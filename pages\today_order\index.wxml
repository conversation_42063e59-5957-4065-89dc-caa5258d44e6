<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <view class="page-title">菜单推送</view>
  </view>

  <!-- 购物篮为空时显示 -->
  <view class="empty-basket" wx:if="{{!basketItems.length && !orderSubmitted}}">
    <image class="empty-image" src="{{emptyCartImage}}" mode="aspectFit" />
    <view class="empty-text">今日暂无点菜</view>
    <view class="go-order-btn" bindtap="goToOrder">去点菜</view>
  </view>

  <!-- 订单已提交时显示 -->
  <view class="order-submitted" wx:if="{{orderSubmitted}}">
    <van-icon name="checked" size="80rpx" color="#00f2ea" />
    <view class="submitted-text">今日已完成点菜</view>
    <view class="submitted-actions">
      <view class="view-menu-btn" bindtap="viewTodayMenu">查看菜单</view>
      <view class="continue-order-btn" bindtap="continueOrder">继续点菜</view>
    </view>
  </view>

  <!-- 购物篮有商品时显示 -->
  <view class="order-content" wx:if="{{basketItems.length && !orderSubmitted}}">
    <view class="order-list">
      <view wx:for="{{basketItems}}" wx:key="id" class="order-item">
        <image
          class="order-item-img"
          src="{{item.img || defaultFoodImage}}"
          mode="aspectFill"
        />
        <view class="order-item-info">
          <view class="order-item-name">{{item.name}}</view>
          <view class="order-item-desc">{{item.desc || '家庭美味佳肴'}}</view>
          <view class="order-item-remark" wx:if="{{item.remark}}"
            >备注：{{item.remark}}</view
          >
        </view>
        <view class="order-item-right">
          <view class="order-item-count">x{{item.count}}</view>
          <view class="delete-btn" bindtap="deleteItem" data-id="{{item.id}}">
            <van-icon name="delete" />
          </view>
        </view>
      </view>
    </view>

    <!-- 用户选择、备注和用餐时间 -->
    <view class="remark-section">
      <!-- 推送人员选择 -->
      <view class="remark-label">推送人员</view>
      <view class="user-selector" bindtap="showUserSelector">
        <view class="user-info" wx:if="{{selectedUser}}">
          <image
            class="user-avatar"
            src="{{selectedUser.avatar || '/assets/image/avator.jpg'}}"
            mode="aspectFill"
          />
          <text class="user-name">{{selectedUser.name}}</text>
        </view>
        <view class="user-placeholder" wx:else>
          <text>请选择推送人员</text>
        </view>
        <van-icon name="arrow-down" />
      </view>

      <view class="remark-label">备注</view>
      <textarea
        class="remark-input"
        placeholder="如忌口、特殊需求等"
        placeholder-class="placeholder-style"
        bindinput="onRemarkInput"
        value="{{remark}}"
        auto-height
        maxlength="200"
        show-confirm-bar="{{false}}"
      ></textarea>

      <view class="remark-label">用餐时间</view>
      <picker
        mode="multiSelector"
        bindchange="bindTimeChange"
        value="{{timeIndex}}"
        range="{{timeArray}}"
      >
        <view class="time-picker">
          <view class="picker-value">{{selectedTime || '请选择用餐时间'}}</view>
          <van-icon name="arrow-down" />
        </view>
      </picker>
    </view>

    <view class="submit-btn" bindtap="submitOrder">推送菜单</view>

    <!-- 历史菜单入口 -->
    <view class="history-entry" bindtap="goToHistory">
      <view class="history-title">
        <van-icon name="clock-o" class="icon-margin" />历史菜单
      </view>
      <van-icon name="arrow" />
    </view>
  </view>

  <!-- 提交确认对话框 -->
  <van-dialog
    use-slot
    title=""
    show="{{ showDialog }}"
    show-cancel-button
    confirm-button-text="确认推送"
    cancel-button-text="再想想"
    bind:confirm="handleConfirm"
    bind:cancel="handleCancel"
    confirm-button-color="#00f2ea"
    cancel-button-color="#fe2c55"
    overlay-style="background-color: rgba(0, 0, 0, 0.7);"
    custom-class="custom-dialog"
    title-class="custom-dialog-title"
    button-class="custom-dialog-button"
    confirm-button-class="confirm-btn"
    cancel-button-class="cancel-btn"
  >
    <view class="dialog-content">
      <view class="dialog-header">
        <van-icon name="checked" color="#00f2ea" size="48rpx" />
        <view class="dialog-title">确认推送以下菜品？</view>
      </view>

      <view class="dialog-items">
        <view wx:for="{{basketItems}}" wx:key="id" class="dialog-item">
          <view class="dialog-item-name">
            <text class="dialog-item-dot"></text>
            <text>{{item.name}}</text>
          </view>
          <text class="dialog-item-count">x {{item.count}}</text>
        </view>
      </view>

      <view class="dialog-info">
        <view class="dialog-user" wx:if="{{selectedUser}}">
          <text class="dialog-user-label">推送人员：</text>
          <text class="dialog-user-content">{{selectedUser.name}}</text>
        </view>

        <view class="dialog-remark" wx:if="{{remark}}">
          <text class="dialog-remark-label">备注：</text>
          <text class="dialog-remark-content">{{remark}}</text>
        </view>

        <view class="dialog-time" wx:if="{{selectedTime}}">
          <text class="dialog-time-label">用餐时间：</text>
          <text class="dialog-time-content">{{selectedTime}}</text>
        </view>

        <view class="dialog-notice">
          <van-icon name="info-o" size="28rpx" color="#ff9800" />
          <text class="dialog-notice-text">
            推送成功后，对方将收到站内通知
            <text
              wx:if="{{subscriptionAuthStatus === 'accept'}}"
              class="auth-success"
              >和微信服务通知</text
            >
            <text
              wx:elif="{{subscriptionAuthStatus === 'reject'}}"
              class="auth-failed"
              >（不含微信通知）</text
            >
            <text
              wx:elif="{{subscriptionAuthStatus === 'failed'}}"
              class="auth-failed"
              >（不含微信通知）</text
            >
            <text
              wx:elif="{{subscriptionAuthStatus === 'devtools'}}"
              class="auth-unknown"
              >（开发环境）</text
            >
            <text wx:else class="auth-unknown">（微信通知状态未知）</text>
          </text>
        </view>
      </view>
    </view>
  </van-dialog>

  <!-- 用户选择弹窗 -->
  <van-popup
    show="{{showUserSelector}}"
    position="bottom"
    round
    bind:close="hideUserSelector"
    custom-style="padding: 32rpx 0;"
  >
    <view class="user-selector-popup">
      <view class="popup-header">
        <text class="popup-title">选择推送人员</text>
        <van-icon name="cross" size="32rpx" bind:click="hideUserSelector" />
      </view>

      <view class="user-list">
        <!-- 有关联用户时显示列表 -->
        <view wx:if="{{familyMembers.length > 0}}">
          <view
            wx:for="{{familyMembers}}"
            wx:key="id"
            class="user-item {{selectedUser && selectedUser.id === item.id ? 'selected' : ''}}"
            bindtap="selectUser"
            data-user-id="{{item.id}}"
          >
            <image
              class="user-item-avatar"
              src="{{item.avatar || '/assets/image/avator.jpg'}}"
              mode="aspectFill"
            />
            <view class="user-item-info">
              <text class="user-item-name">{{item.name}}</text>
              <text class="user-item-role">{{item.role || '关联用户'}}</text>
            </view>
            <van-icon
              wx:if="{{selectedUser && selectedUser.id === item.id}}"
              name="success"
              color="#00f2ea"
              size="32rpx"
            />
          </view>
        </view>

        <!-- 无关联用户时显示缺省页 -->
        <view wx:else class="empty-users-state">
          <view class="empty-icon">
            <van-icon name="friends-o" size="60rpx" color="#D1D5DB" />
          </view>
          <view class="empty-text">暂无关联用户</view>
          <view class="empty-desc">添加关联用户后可为他们下单</view>
          <view class="empty-action">
            <van-button
              size="small"
              type="primary"
              plain
              bind:click="goToUserConnection"
            >
              去添加关联用户
            </van-button>
          </view>
        </view>
      </view>
    </view>
  </van-popup>

  <!-- 提交成功提示 -->
  <van-toast id="van-toast" />
</view>
