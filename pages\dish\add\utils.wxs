// 菜品标签工具函数
var utils = {
  // 检查标签是否被选中
  isTagSelected: function (selectedTags, tagValue) {
    if (!selectedTags || !tagValue) {
      return false;
    }

    for (var i = 0; i < selectedTags.length; i++) {
      if (selectedTags[i] === tagValue) {
        return true;
      }
    }
    return false;
  },

  // 获取标签的CSS类名
  getTagClass: function (selectedTags, tagValue) {
    var baseClass = 'tag-item';
    if (utils.isTagSelected(selectedTags, tagValue)) {
      return baseClass + ' selected';
    }
    return baseClass;
  },

  // 获取标签文字颜色
  getTagTextColor: function (selectedTags, tagValue, tagColor) {
    if (utils.isTagSelected(selectedTags, tagValue)) {
      return '#fff';
    }
    return tagColor;
  },

  // 获取标签背景色
  getTagBgColor: function (selectedTags, tagValue, tagColor) {
    if (utils.isTagSelected(selectedTags, tagValue)) {
      return tagColor;
    }
    return 'transparent';
  },

  // 检查是否显示勾选标记
  showCheckMark: function (selectedTags, tagValue) {
    return utils.isTagSelected(selectedTags, tagValue);
  }
};

module.exports = {
  isTagSelected: utils.isTagSelected,
  getTagClass: utils.getTagClass,
  getTagTextColor: utils.getTagTextColor,
  getTagBgColor: utils.getTagBgColor,
  showCheckMark: utils.showCheckMark
};
