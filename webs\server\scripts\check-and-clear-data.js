require('dotenv').config();
const {PrismaClient} = require('@prisma/client');

const prisma = new PrismaClient();

async function checkUsers() {
  try {
    console.log('🔍 查看当前数据库中的用户...');
    const users = await prisma.user.findMany({
      select: {
        id: true,
        name: true,
        phone: true,
        role: true,
        createdAt: true
      },
      orderBy: {createdAt: 'desc'}
    });

    console.log('📊 当前用户列表:');
    users.forEach((user, index) => {
      console.log(
        `${index + 1}. ${user.name} (ID: ${user.id}, 手机: ${
          user.phone || '无'
        }, 角色: ${user.role})`
      );
    });

    console.log(`\n总计: ${users.length} 个用户`);

    // 查找张三和李四
    const zhangsan = users.find(u => u.name === '张三');
    const lisi = users.find(u => u.name === '李四');

    console.log('\n🎯 目标用户检查:');
    console.log('张三:', zhang<PERSON> ? '✅ 找到' : '❌ 未找到');
    console.log('李四:', lisi ? '✅ 找到' : '❌ 未找到');

    if (zhangsan) console.log('张三 ID:', zhangsan.id);
    if (lisi) console.log('李四 ID:', lisi.id);

    return {zhangsan, lisi, allUsers: users};
  } catch (error) {
    console.error('❌ 查询失败:', error);
    throw error;
  }
}

async function clearDataExceptUsers(keepUserIds) {
  try {
    console.log('\n🧹 开始清空数据库数据...');
    console.log('保留的用户ID:', keepUserIds);

    // 按照外键依赖关系的顺序删除数据

    // 1. 删除推荐菜单
    console.log('1️⃣ 清理推荐菜单...');
    const deletedRecommendedMenus = await prisma.recommendedMenu.deleteMany({
      where: {
        userId: {
          notIn: keepUserIds
        }
      }
    });
    console.log(`   删除了 ${deletedRecommendedMenus.count} 条推荐菜单记录`);

    // 2. 删除订单推送
    console.log('2️⃣ 清理订单推送...');
    const deletedOrderPushes = await prisma.orderPush.deleteMany({
      where: {
        OR: [
          {pushedBy: {notIn: keepUserIds}},
          {targetUserId: {notIn: keepUserIds}}
        ]
      }
    });
    console.log(`   删除了 ${deletedOrderPushes.count} 条订单推送记录`);

    // 3. 删除留言接收者
    console.log('3️⃣ 清理留言接收者...');
    const deletedMessageRecipients = await prisma.messageRecipient.deleteMany({
      where: {
        userId: {
          notIn: keepUserIds
        }
      }
    });
    console.log(`   删除了 ${deletedMessageRecipients.count} 条留言接收者记录`);

    // 4. 删除用户连接
    console.log('4️⃣ 清理用户连接...');
    const deletedConnections = await prisma.userConnection.deleteMany({
      where: {
        OR: [
          {senderId: {notIn: keepUserIds}},
          {receiverId: {notIn: keepUserIds}}
        ]
      }
    });
    console.log(`   删除了 ${deletedConnections.count} 条用户连接记录`);

    // 5. 删除通知
    console.log('5️⃣ 清理通知...');
    const deletedNotifications = await prisma.notification.deleteMany({
      where: {
        OR: [{userId: {notIn: keepUserIds}}, {senderId: {notIn: keepUserIds}}]
      }
    });
    console.log(`   删除了 ${deletedNotifications.count} 条通知记录`);

    // 6. 删除留言
    console.log('6️⃣ 清理留言...');
    const deletedMessages = await prisma.message.deleteMany({
      where: {
        userId: {
          notIn: keepUserIds
        }
      }
    });
    console.log(`   删除了 ${deletedMessages.count} 条留言记录`);

    // 7. 删除订单
    console.log('7️⃣ 清理订单...');
    const deletedOrders = await prisma.order.deleteMany({
      where: {
        userId: {
          notIn: keepUserIds
        }
      }
    });
    console.log(`   删除了 ${deletedOrders.count} 条订单记录`);

    // 8. 删除菜单项
    console.log('8️⃣ 清理菜单项...');
    const menusToDelete = await prisma.menu.findMany({
      where: {
        createdBy: {
          notIn: keepUserIds
        }
      },
      select: {id: true}
    });

    if (menusToDelete.length > 0) {
      const menuIds = menusToDelete.map(m => m.id);
      const deletedMenuItems = await prisma.menuItem.deleteMany({
        where: {
          menuId: {
            in: menuIds
          }
        }
      });
      console.log(`   删除了 ${deletedMenuItems.count} 条菜单项记录`);
    }

    // 9. 删除菜单
    console.log('9️⃣ 清理菜单...');
    const deletedMenus = await prisma.menu.deleteMany({
      where: {
        createdBy: {
          notIn: keepUserIds
        }
      }
    });
    console.log(`   删除了 ${deletedMenus.count} 条菜单记录`);

    // 10. 删除菜品
    console.log('🔟 清理菜品...');
    const deletedDishes = await prisma.dish.deleteMany({
      where: {
        createdBy: {
          notIn: keepUserIds
        }
      }
    });
    console.log(`   删除了 ${deletedDishes.count} 条菜品记录`);

    // 11. 删除其他用户
    console.log('1️⃣1️⃣ 清理其他用户...');
    const deletedUsers = await prisma.user.deleteMany({
      where: {
        id: {
          notIn: keepUserIds
        }
      }
    });
    console.log(`   删除了 ${deletedUsers.count} 个用户账号`);

    console.log('\n✅ 数据清理完成！');

    return {
      deletedRecommendedMenus: deletedRecommendedMenus.count,
      deletedOrderPushes: deletedOrderPushes.count,
      deletedMessageRecipients: deletedMessageRecipients.count,
      deletedConnections: deletedConnections.count,
      deletedNotifications: deletedNotifications.count,
      deletedMessages: deletedMessages.count,
      deletedOrders: deletedOrders.count,
      deletedMenus: deletedMenus.count,
      deletedDishes: deletedDishes.count,
      deletedUsers: deletedUsers.count
    };
  } catch (error) {
    console.error('❌ 清理数据失败:', error);
    throw error;
  }
}

async function main() {
  try {
    // 1. 检查用户
    const {zhangsan, lisi, allUsers} = await checkUsers();

    if (!zhangsan || !lisi) {
      console.log('\n❌ 未找到张三或李四，请确认用户名是否正确');
      console.log('当前数据库中的用户名:');
      allUsers.forEach(user => console.log(`  - ${user.name}`));
      return;
    }

    // 2. 确认操作
    console.log('\n⚠️  即将清空数据库中除张三和李四之外的所有数据！');
    console.log('保留的用户:');
    console.log(`  - 张三 (ID: ${zhangsan.id})`);
    console.log(`  - 李四 (ID: ${lisi.id})`);
    console.log(`\n将删除其他 ${allUsers.length - 2} 个用户及其相关数据`);

    // 3. 执行清理
    const keepUserIds = [zhangsan.id, lisi.id];
    const result = await clearDataExceptUsers(keepUserIds);

    // 4. 显示结果
    console.log('\n📊 清理统计:');
    console.log(`  - 推荐菜单: ${result.deletedRecommendedMenus} 条`);
    console.log(`  - 订单推送: ${result.deletedOrderPushes} 条`);
    console.log(`  - 留言接收者: ${result.deletedMessageRecipients} 条`);
    console.log(`  - 用户连接: ${result.deletedConnections} 条`);
    console.log(`  - 通知: ${result.deletedNotifications} 条`);
    console.log(`  - 留言: ${result.deletedMessages} 条`);
    console.log(`  - 订单: ${result.deletedOrders} 条`);
    console.log(`  - 菜单: ${result.deletedMenus} 条`);
    console.log(`  - 菜品: ${result.deletedDishes} 条`);
    console.log(`  - 用户: ${result.deletedUsers} 个`);

    // 5. 验证结果
    console.log('\n🔍 验证清理结果...');
    const remainingUsers = await prisma.user.findMany({
      select: {id: true, name: true}
    });
    console.log('剩余用户:');
    remainingUsers.forEach(user => {
      console.log(`  ✅ ${user.name} (ID: ${user.id})`);
    });
  } catch (error) {
    console.error('❌ 操作失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
