#!/bin/bash
# 自动部署脚本
exec > >(tee -a /www/deploy.log) 2>&1
set -e

echo "🚀 开始自动部署..."
date --date='0 days ago' "+%Y-%m-%d %H:%M:%S"
# 设置项目路径
PROJECT_PATH="/www/wwwroot/www.huanglun.asia/api"
SERVER_PATH="$PROJECT_PATH/webs/server"
ADMIN_PATH="$PROJECT_PATH/webs/admin"

# 进入项目目录
cd $PROJECT_PATH
#删除之前的代码 
rm -rf $PROJECT_PATH/webs/*
# 拉取最新代码
echo "📥 拉取最新代码..."
git pull origin master --verbose

# 进入服务器目录
cd $SERVER_PATH

# 安装/更新依赖
echo "📦 安装依赖..."
rm -rf node_modules
rm -f package-lock.json yarn.lock pnpm-lock.yaml

npm install --production

# 进入后台管理系统 
# cd $ADMIN_PATH

# 安装/更新依赖
# echo "📦 安装后台依赖..."
# rm -rf node_modules
# rm -f package-lock.json yarn.lock pnpm-lock.yaml

# npm install --production

# 切换到MySQL schema
echo "🔄 切换数据库配置..."
cp prisma/schema.mysql.prisma prisma/schema.prisma

# 生成Prisma客户端
echo "🔧 生成Prisma客户端..."
npx prisma generate

# 同步数据库结构
echo "🗄️ 同步数据库..."
npx prisma db push

# 重启PM2服务
echo "🔄 重启服务..."
pm2 restart nannan-api || pm2 start src/app.js --name nannan-api --env production

# 保存PM2配置
pm2 save

# 后续在启动后台管理系统


echo "✅ 部署完成！"

# 显示服务状态
pm2 status
